// // Copyright (c) 2023 Furkan KIYIKCI. All Rights Reserved.

#include "ModularCharacterComponent.h"

#include "ModularCharacterPlugin.h"
#include "ModularCharacterUtils.h"
#include "Interfaces/ModularCharacterInterface.h"
#include "Net/UnrealNetwork.h"
#include "Net/Core/PushModel/PushModel.h"


// Sets default values for this component's properties
UModularCharacterComponent::UModularCharacterComponent()
{
	// Set this component to be initialized when the game starts, and to be ticked every frame.  You can turn these features
	// off to improve performance if you don't need them.
	PrimaryComponentTick.bCanEverTick = false;
	bWantsInitializeComponent = true;
	SetIsReplicatedByDefault(true);
}

void UModularCharacterComponent::ReadyForReplication()
{
	Super::ReadyForReplication();

	// // Get the owner actor of this component

	if (const bool bIsClient = (GetWorld()->GetNetMode() == NM_Client) || (GetWorld()->GetNetMode() == NM_Standalone))
	{
		if (Cast<APawn>(GetOwner())->IsLocallyControlled())
		{
			// Get Character Parts from game instance
			if (!InitializeCharacterParts()) return;
			if (CharacterPartsArray.IsEmpty())
				CharacterPartsArray = DefaultCharacterParts;
			// Set Character Parts
			if ((GetWorld()->GetNetMode() == NM_Client))
				Server_SetCharacterParts(CharacterPartsArray);
			else
			{
				if (GetWorld()->GetNetMode() == NM_ListenServer)
				{
					Server_SetCharacterParts(CharacterPartsArray);
				}
				else
				{
					InitializeSkeletalMeshes();
				}
			}
		}
	}
}

// Called when the game starts
void UModularCharacterComponent::BeginPlay()
{
	Super::BeginPlay();

	SetIsReplicated(true);
}

void UModularCharacterComponent::GetDefaultCharacterPartsFromDataTable()
{
	if (ConstructionTable)
	{
		DefaultCharacterParts.Empty();
		// Get the row map

		// Iterate through the rows in the table
		const TMap<FName, uint8*>& RowMap = ConstructionTable->GetRowMap();
		for (auto& RowEntry : RowMap)
		{
			// Get the row data as a generic UObject
			const auto RowDataObject = RowEntry.Value;
			const UScriptStruct* StructType = ConstructionTable->GetRowStruct();
			if (RowDataObject)
			{
				// Iterate through the properties of the row data structure
				for (TFieldIterator<FProperty> PropertyIt(StructType); PropertyIt; ++PropertyIt)
				{
					// Check if the property's name matches the column name
					if (FProperty* Property = *PropertyIt; Property->IsA(FStructProperty::StaticClass()))
					{
						const FStructProperty* StructProperty = CastField<FStructProperty>(Property);

						void* DataPtr = StructProperty->ContainerPtrToValuePtr<void>(RowDataObject);

						// Ensure the size matches your expected struct size
						if (DataPtr && StructProperty->GetSize() == sizeof(FCharacterPart))
						{
							if (const FCharacterPart* CharacterPartColumn = static_cast<FCharacterPart*>(DataPtr))
							{
								if (!DefaultCharacterParts.Contains(CharacterPartColumn->Name))
									DefaultCharacterParts.AddUnique(*CharacterPartColumn);

								// UE_LOG(LogModularCharacterPlugin, Warning, TEXT("%s"), *CharacterPartColumn->Name);
							}
						}
					}
				}
			}
		}
	}
}

void UModularCharacterComponent::OnComponentCreated()
{
	Super::OnComponentCreated();
	if (GetOwner()->GetNetMode() != NM_DedicatedServer)
	{
		const USkeletalMeshComponent* ParentSkeletalMeshComponent = GetOwner()->FindComponentByClass<
			USkeletalMeshComponent>();

		if (ParentSkeletalMeshComponent)
		{
			OldEditorPart.Mesh = ParentSkeletalMeshComponent->GetSkeletalMeshAsset();
			OldEditorPart.Materials.Empty();
			for (const auto Material : ParentSkeletalMeshComponent->GetMaterials())
			{
				OldEditorPart.Materials.Add(Material);
			}
		}
		if (DefaultCharacterParts.IsEmpty())
			GetDefaultCharacterPartsFromDataTable();
		if (CharacterPartsArray.IsEmpty())
			CharacterPartsArray = DefaultCharacterParts;
		if (bSelectBestOptionsForMe)
			SelectBestOptions();
		InitializeSkeletalMeshes();
	}
}

void UModularCharacterComponent::InitializeComponent()
{
	Super::InitializeComponent();
	InitializeSkeletalMeshes();
	// if (GetOwner()->GetNetMode() != NM_DedicatedServer)
	// {
	// 	if (DefaultCharacterParts.IsEmpty())
	// 		GetDefaultCharacterPartsFromDataTable();
	// 	if (CharacterPartsArray.IsEmpty())
	// 		CharacterPartsArray = DefaultCharacterParts;
	// 	if (bSelectBestOptionsForMe)
	// 		SelectBestOptions();
	// 	InitializeSkeletalMeshes();
	// }
}


void UModularCharacterComponent::OnUnregister()
{
	Super::OnUnregister();

	if (bAutoCreateSkeletalMeshes)
	{
		const USkeletalMeshComponent* ParentSkeletalMeshComponent = GetOwner()->FindComponentByClass<
			USkeletalMeshComponent>();
		// clear all child skeletal meshes
		auto ChildSkeletalMeshes = GetChildSkeletalMeshComponents(ParentSkeletalMeshComponent);
		for (USceneComponent*
		     Child
		     : ChildSkeletalMeshes)
		{
			Child->DetachFromComponent(FDetachmentTransformRules::KeepRelativeTransform);
			Child->DestroyComponent();
		}
	}
	USkeletalMeshComponent* ParentSkeletalMeshComponent = GetOwner()->FindComponentByClass<
		USkeletalMeshComponent>();
	if (ParentSkeletalMeshComponent && OldEditorPart.Mesh)
	{
		ParentSkeletalMeshComponent->SetSkinnedAssetAndUpdate(OldEditorPart.Mesh.Get());
		for (auto MaterialIndex = 0; MaterialIndex < OldEditorPart.Materials.Num(); MaterialIndex++)
		{
			ParentSkeletalMeshComponent->SetMaterial(MaterialIndex, OldEditorPart.Materials[MaterialIndex].Get());
		}
	}
}

#if WITH_EDITOR
void UModularCharacterComponent::PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent)
{
	Super::PostEditChangeProperty(PropertyChangedEvent);
	// Check the conditions and reset the property if necessary
	// if (PropertyChangedEvent.Property != nullptr && PropertyChangedEvent.Property->GetFName() ==
	// 	GET_MEMBER_NAME_CHECKED(UModularCharacterComponent, bUseParentSkeletalMeshAsRoot))
	// {

	// }

	if (bSelectBestOptionsForMe)
	{
		bAutoCreateSkeletalMeshes = false;
		bUseParentSkeletalMeshAsRoot = false;
	}
	if (bAutoCreateSkeletalMeshes)
	{
		// bUseParentSkeletalMeshAsRoot = false;
	}
}
#endif


// Called every frame
void UModularCharacterComponent::TickComponent(float DeltaTime, ELevelTick TickType,
                                               FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
}

// Gets Character parts from current game instance
bool UModularCharacterComponent::InitializeCharacterParts()
{
	TArray<FCharacterPart> InstanceParts;
	// ADD INTERFACE CHECK
	UGameInstance* Instance = GetWorld()->GetGameInstance();
	// if implemented in c++
	if (const auto& ModularCharacterInstance = Cast<IModularCharacterInterface>(Instance))
	{
		InstanceParts = ModularCharacterInstance->Execute_GetCharacterParts(Instance);
	}
	// else if implemented in blueprint
	else if (Instance->Implements<UModularCharacterInterface>())
	{
		InstanceParts = IModularCharacterInterface::Execute_GetCharacterParts(Instance);
	}
	if (InstanceParts.IsEmpty())
	{
		SyncCharacterParts();
	}
	Internal_SetCharacterParts(InstanceParts);

	return true;
}

void UModularCharacterComponent::Internal_SetCharacterParts(const TArray<FCharacterPart>& InCharacterParts)
{
	this->CharacterPartsArray = InCharacterParts;
	MARK_PROPERTY_DIRTY_FROM_NAME(UModularCharacterComponent, CharacterPartsArray, this);
}


void UModularCharacterComponent::Server_SetCharacterParts_Implementation(const TArray<FCharacterPart>& InCharacterParts)
{
	const auto PreviousParts = this->CharacterPartsArray;
	Internal_SetCharacterParts(InCharacterParts);
	if (GetOwner()->GetNetMode() == NM_ListenServer)
	{
		OnRep_CharacterPartsUpdated(PreviousParts);
	}
	UE_LOG(LogModularCharacterPlugin, Warning,
	       TEXT("Character Parts is initialited for %d : %d"), GetWorld()->GetNetMode(), InCharacterParts.Num());
}

void UModularCharacterComponent::SetCharacterParts_Implementation(const TArray<FCharacterPart>& InCharacterParts)
{
	Server_SetCharacterParts(InCharacterParts);
}


// Sets game instances character parts for persistence
void UModularCharacterComponent::SyncCharacterParts() const
{
	UGameInstance* Instance = GetWorld()->GetGameInstance();
	// if implemented in c++
	if (const auto& ModularCharacterInstance = Cast<IModularCharacterInterface>(Instance))
	{
		ModularCharacterInstance->Execute_SetCharacterParts(Instance, CharacterPartsArray);
	}
	// else if implemented in blueprint
	else if (Instance->Implements<UModularCharacterInterface>())
	{
		IModularCharacterInterface::Execute_SetCharacterParts(Instance, CharacterPartsArray);
	}
}


// Applies given character part to skeleton according to its name
void UModularCharacterComponent::Internal_ApplyCharacterPart(const FCharacterPart& CharacterPart,
                                                             USkeletalMeshComponent* ChiLdSkeletalMesh,
                                                             USceneComponent* ParentComponent)
{
	const FString PartName = CharacterPart.Name;
	const auto MeshPart = CharacterPart.Mesh.LoadSynchronous();
	const auto MaterialsPart = CharacterPart.Materials;

	if (!MeshPart) return;
	if (MaterialsPart.IsEmpty()) return;

	if (!ChiLdSkeletalMesh)
	{
		if (SkeletalParts.Contains(PartName))
			ChiLdSkeletalMesh = SkeletalParts[PartName].Get();
	}
	if (ChiLdSkeletalMesh)
	{
		ChiLdSkeletalMesh->SetSkinnedAssetAndUpdate(MeshPart, false);
		ChiLdSkeletalMesh->SetVisibility(true);
		ChiLdSkeletalMesh->SetHiddenInGame(false);
		for (auto MaterialIndex = 0; MaterialIndex < MaterialsPart.Num(); MaterialIndex++)
		{
			const auto MaterialInterface = MaterialsPart[MaterialIndex].LoadSynchronous();
			ChiLdSkeletalMesh->SetMaterial(MaterialIndex, MaterialInterface);
		}
		if (bUseAnimationsPerInstance)
		{
			if (CharacterAnimClass)
				ChiLdSkeletalMesh->SetAnimInstanceClass(CharacterAnimClass);
		}


		// Skip this part if parental skeletal mesh is none or parental skeletal mesh is already same as child skeletal mesh
		if (ParentComponent == nullptr)
		{
			if (CharacterAnimClass)
				ChiLdSkeletalMesh->SetAnimInstanceClass(CharacterAnimClass);
			return;
		};
		if (ChiLdSkeletalMesh == ParentComponent)
		{
			// ChiLdSkeletalMesh->AnimClass = CharacterAnimClass;
			OldEditorPart.Mesh = ChiLdSkeletalMesh->GetSkeletalMeshAsset();
			OldEditorPart.Materials.Empty();
			for (const auto Material : ChiLdSkeletalMesh->GetMaterials())
			{
				OldEditorPart.Materials.Add(Material);
			}
			if (ChiLdSkeletalMesh->AnimClass == nullptr)
				UE_LOG(LogModularCharacterPlugin, Warning,
			       TEXT("Animation instance could not found please select an animation class "));
			return;
		}


		if (const auto ParentSkeletalMesh = Cast<USkeletalMeshComponent>(ParentComponent))
			if (!bUseAnimationsPerInstance)
				ChiLdSkeletalMesh->SetLeaderPoseComponent(ParentSkeletalMesh, true);


		if (!ChiLdSkeletalMesh->IsAttachedTo(ParentComponent))
		{
			ChiLdSkeletalMesh->AttachToComponent(ParentComponent,
			                                     FAttachmentTransformRules::SnapToTargetIncludingScale);
		}
		ChiLdSkeletalMesh->SetupAttachment(ParentComponent);

		if (!ChiLdSkeletalMesh->IsRegistered())
			ChiLdSkeletalMesh->RegisterComponent();
		// ParentComponent->GetOwner()->AddInstanceComponent(ChiLdSkeletalMesh);
	}
}

void UModularCharacterComponent::Server_ApplyCharacterPart_Implementation(const FCharacterPart& CharacterPart)
{
	// Use FindByPredicate to find the first element that matches the predicate
	for (auto Index = 0; Index < CharacterPartsArray.Num(); Index++)
	{
		if (CharacterPartsArray[Index].Name == CharacterPart.Name)
		{
			CharacterPartsArray[Index] = CharacterPart;
			MARK_PROPERTY_DIRTY_FROM_NAME(UModularCharacterComponent, CharacterPartsArray, this);
		}
	}
}

// Refactor to set character part
void UModularCharacterComponent::ApplyCharacterPart(const FCharacterPart& CharacterPart)
{
	//if not client
	if (const ENetMode NetMode = GetOwner()->GetNetMode(); NetMode == NM_Standalone || NetMode == NM_DedicatedServer)
	{
		// Use FindByPredicate to find the first element that matches the predicate
		Internal_ApplyCharacterPart(CharacterPart);

		if (NetMode == NM_Standalone)
			SyncCharacterParts();
	}
	else
	{
		Server_ApplyCharacterPart(CharacterPart);
	}
}

USkeletalMeshComponent* UModularCharacterComponent::GetSkeletalMeshComponent(const FString& Name)
{
	if (SkeletalParts.Contains(Name))
	{
		return SkeletalParts[Name].Get();
	}
	return {};
}


// Changes meshes and materials to given character part also sets leader to skeleton
bool UModularCharacterComponent::SetupSkeletalMeshComponent(USkeletalMeshComponent* SkeletalMesh,
                                                            const FCharacterPart& CharacterPart,
                                                            USceneComponent* ParentComponent)
{
	if (!SkeletalMesh) return false;
	// add to the skeletal part array;
	SkeletalParts.Add(CharacterPart.Name, SkeletalMesh);
	Internal_ApplyCharacterPart(CharacterPart, SkeletalMesh, ParentComponent);

	//without this, component wont show up in properties
	return true;
}


TArray<USkeletalMeshComponent*> UModularCharacterComponent::GetChildSkeletalMeshComponents(
	const USkeletalMeshComponent* Parent)
{
	// Check if the owner actor has a SkeletalMeshComponent
	TArray<USkeletalMeshComponent*> SkeletalMeshComponents;
	// Get an array of all attached components
	TArray<USceneComponent*> AttachedComponents;
	Parent->GetChildrenComponents(false, AttachedComponents);
	for (USceneComponent* AttachedComponent : AttachedComponents)
	{
		// Check if the attached component is a SkeletalMeshComponent

		if (USkeletalMeshComponent* ChildSkeletalMeshComponent = Cast<USkeletalMeshComponent>(AttachedComponent))
		{
			// This is a child skeletal mesh component, you can work with it here
			SkeletalMeshComponents.Add(ChildSkeletalMeshComponent);
		}
	}
	return SkeletalMeshComponents;
}

// Initializes all skeletal meshes from FCharacterPart array
bool UModularCharacterComponent::InitializeSkeletalMeshes()
{
	// if (!bUseParentSkeletalMeshAsRoot)
	// {
	// 	USkeletalMeshComponent* ParentSkeletalMeshComponent = GetOwner()->FindComponentByClass<
	// 		USkeletalMeshComponent>();
	// 	if (ParentSkeletalMeshComponent && OldEditorPart.Mesh)
	// 	{
	// 		ParentSkeletalMeshComponent->SetSkinnedAssetAndUpdate(OldEditorPart.Mesh.Get());
	// 		OldEditorPart.Materials.Empty();
	// 		for (const auto Material : ParentSkeletalMeshComponent->GetMaterials())
	// 		{
	// 			OldEditorPart.Materials.Add(Material);
	// 		}
	// 	}
	// }

	// create skeletal mesh components according to the paths and setup them 
	if (bAutoCreateSkeletalMeshes)
	{
		if (!ConstructSkeletalMeshes(!bUseParentSkeletalMeshAsRoot)) return false;
	}
	else
	{
		if (!ConstructPreCreatedSkeletalMeshes(!bUseParentSkeletalMeshAsRoot)) return false;
	}

	UE_LOG(LogModularCharacterPlugin, Warning,
	       TEXT("SkeletalMeshes successfully initialized"));

	return true;
}

TArray<USkeletalMeshComponent*> UModularCharacterComponent::GetAllSkeletonsRecursively(USkeletalMeshComponent* Parent)
{
	// Check if the owner actor has a SkeletalMeshComponent
	TArray<USkeletalMeshComponent*> SkeletalMeshComponents;
	// Get an array of all attached components
	TArray<USceneComponent*> AttachedComponents;
	GetOwner()->GetRootComponent()->GetChildrenComponents(true, AttachedComponents);
	for (USceneComponent* AttachedComponent : AttachedComponents)
	{
		// Check if the attached component is a SkeletalMeshComponent

		if (USkeletalMeshComponent* ChildSkeletalMeshComponent = Cast<USkeletalMeshComponent>(AttachedComponent))
		{
			// This is a child skeletal mesh component, you can work with it here
			SkeletalMeshComponents.Add(ChildSkeletalMeshComponent);
		}
	}
	return SkeletalMeshComponents;
}

bool UModularCharacterComponent::ConstructPreCreatedSkeletalMeshes(const bool& bUseParentSkeletalMesh)
{
	const AActor* OwnerActor = GetOwner();
	if (!OwnerActor) return false;


	// Get Already created skeletal meshes from parent actor
	if (USkeletalMeshComponent* ParentSkeletalMeshComponent = OwnerActor->FindComponentByClass<
		USkeletalMeshComponent>())
	{
		if (CharacterAnimClass.Get() == nullptr)
			CharacterAnimClass = ParentSkeletalMeshComponent->AnimClass;
		auto SkeletalMeshComponents = bForceNameMatchWithComponent
			                              ? GetAllSkeletonsRecursively(ParentSkeletalMeshComponent)
			                              : GetChildSkeletalMeshComponents(ParentSkeletalMeshComponent);


		if (bUseParentSkeletalMesh)
		{
			// Ensure parental mesh is not hidden 
			ParentSkeletalMeshComponent->SetHiddenInGame(false);
			ParentSkeletalMeshComponent->SetVisibility(true);
		}
		else
		{
			SkeletalMeshComponents.Add(ParentSkeletalMeshComponent);
		}

		if (!bForceNameMatchWithComponent)
			if (SkeletalMeshComponents.Num() != CharacterPartsArray.Num())
			{
				UE_LOG(LogModularCharacterPlugin, Warning,
				       TEXT("Founded skeleton count is %d  Required skeleton count is %d "),
				       SkeletalMeshComponents.Num(),
				       CharacterPartsArray.Num());

				if (CharacterPartsArray.Num() - SkeletalMeshComponents.Num() == 1)
				{
					if (bUseParentSkeletalMesh)
					{
						UE_LOG(LogModularCharacterPlugin, Warning,
						       TEXT("Please consider useParentSkeletalMeshAsRoot=false option"));
					}
				}
				else if (CharacterPartsArray.Num() - SkeletalMeshComponents.Num() == -1)
				{
					if (!bUseParentSkeletalMesh)
					{
						UE_LOG(LogModularCharacterPlugin, Warning,
						       TEXT("Please consider useParentSkeletalMeshAsRoot=true option"));
					}
				}

				return false;
			}
		const auto PreviousSkeletalPartCount = SkeletalParts.Num();
		SkeletalParts.Empty(PreviousSkeletalPartCount);
		for (int32 Index = 0; Index < SkeletalMeshComponents.Num(); ++Index)
		{
			if (bForceNameMatchWithComponent)
			{
				auto SK_Name = SkeletalMeshComponents[Index]->GetName();
				UE_LOG(LogModularCharacterPlugin, Warning, TEXT("SK_Name: %s"), *SK_Name);
				const FCharacterPart* NamedPart = CharacterPartsArray.FindByKey<FString>(
					SkeletalMeshComponents[Index]->GetName());
				
				if (!NamedPart) continue;
				
				USkeletalMeshComponent* SkeletalMesh = SkeletalMeshComponents[Index];
				SetupSkeletalMeshComponent(SkeletalMesh, *NamedPart, ParentSkeletalMeshComponent);
			}
			else
			{
				USkeletalMeshComponent* SkeletalMesh = SkeletalMeshComponents[Index];
				SetupSkeletalMeshComponent(SkeletalMesh, CharacterPartsArray[Index], ParentSkeletalMeshComponent);
			}
		}
	}
	return true;
}


// Auto created skeletal mesh component initialization disabled for now
bool UModularCharacterComponent::ConstructSkeletalMeshes(const bool& bUseParentSkeletalMesh)
{
	if (CharacterPartsArray.IsEmpty())
		return false;

	const auto OwnerActor = GetOwner();
	if (!OwnerActor) return false;

	USkeletalMeshComponent* ParentSkeletalMeshComponent = OwnerActor->FindComponentByClass<
		USkeletalMeshComponent>();

	if (CharacterAnimClass.Get() == nullptr)
		CharacterAnimClass = ParentSkeletalMeshComponent->AnimClass;

	const auto PreviousSkeletalPartCount = SkeletalParts.Num();
	SkeletalParts.Empty(PreviousSkeletalPartCount);

	if (!ParentSkeletalMeshComponent)
	{
		const auto CharacterPart = CharacterPartsArray[0];
		// USkeletalMeshComponent* SkeletalMesh = NewObject<USkeletalMeshComponent>(
		// 	OwnerActor, USkeletalMeshComponent::StaticClass(),
		// 	FName(CharacterPart.Name),
		// 	RF_NoFlags);
		USkeletalMeshComponent* SkeletalMesh = CreateDefaultSubobject<
			USkeletalMeshComponent>(FName(CharacterPart.Name));

		ParentSkeletalMeshComponent = SkeletalMesh;
		SetupSkeletalMeshComponent(ParentSkeletalMeshComponent, CharacterPart, OwnerActor->GetRootComponent());
	}
	else
	{
		// clear all child skeletal meshes
		auto ChildSkeletalMeshes = GetChildSkeletalMeshComponents(ParentSkeletalMeshComponent);
		for (USceneComponent*
		     Child
		     : ChildSkeletalMeshes)
		{
			Child->DetachFromComponent(FDetachmentTransformRules::KeepRelativeTransform);
			Child->DestroyComponent();
		}
	}

	if (!bUseParentSkeletalMesh)
	{
		// Ensure parental mesh is not hidden 
		ParentSkeletalMeshComponent->SetHiddenInGame(false);
		ParentSkeletalMeshComponent->SetVisibility(true);
		// if (CharacterAnimClass && !ParentSkeletalMeshComponent->AnimClass)
		// 	ParentSkeletalMeshComponent->AnimClass = CharacterAnimClass;
	}
	else
	{
		const auto CharacterPart = CharacterPartsArray[0];
		SetupSkeletalMeshComponent(ParentSkeletalMeshComponent, CharacterPart);
		ParentSkeletalMeshComponent->SetHiddenInGame(false);
		ParentSkeletalMeshComponent->SetVisibility(true);
	}
	for (auto Index = !bUseParentSkeletalMesh ? 0 : 1; Index < CharacterPartsArray.Num(); Index++)
	{
		const auto CharacterPart = CharacterPartsArray[Index];
		USkeletalMeshComponent* SkeletalMesh = NewObject<USkeletalMeshComponent>(
			OwnerActor, USkeletalMeshComponent::StaticClass(),
			FName(CharacterPart.Name),
			RF_NoFlags, ParentSkeletalMeshComponent);
		SetupSkeletalMeshComponent(SkeletalMesh, CharacterPart, ParentSkeletalMeshComponent);
	}
	return true;
}

void UModularCharacterComponent::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);

	FDoRepLifetimeParams SharedParams;
	SharedParams.bIsPushBased = true;
	SharedParams.RepNotifyCondition = REPNOTIFY_Always;
	DOREPLIFETIME_WITH_PARAMS_FAST(UModularCharacterComponent, CharacterPartsArray, SharedParams);
}

void UModularCharacterComponent::OnRep_CharacterPartsUpdated(const TArray<FCharacterPart>& PreviousState)
{
	const auto PreviousParts = this->CharacterPartsArray;
	if (GetOwner()->GetNetMode() == NM_ListenServer)
	{
		InitializeSkeletalMeshes();
		OnCharacterPartsAreChanged.Broadcast(CharacterPartsArray);
		return;
	}
	if (PreviousState.Num() == CharacterPartsArray.Num())
	{
		for (auto CharacterPart : CharacterPartsArray)
		{
			if (CharacterPartsArray.Contains(CharacterPart))
				Internal_ApplyCharacterPart(CharacterPart);
			else
			{
				InitializeSkeletalMeshes();
				return;
			}
		}
	}
	else
	{
		InitializeSkeletalMeshes();
	}
	OnCharacterPartsAreChanged.Broadcast(CharacterPartsArray);
}

void UModularCharacterComponent::SelectBestOptions()
{
	const USkeletalMeshComponent* ParentSkeletalMeshComponent = GetOwner()->FindComponentByClass<
		USkeletalMeshComponent>();

	if (!ParentSkeletalMeshComponent)
	{
		bAutoCreateSkeletalMeshes = true;
	}
	else
	{
		if (const auto SkeletalMeshComponents = GetChildSkeletalMeshComponents(ParentSkeletalMeshComponent);
			SkeletalMeshComponents.Num() != CharacterPartsArray.Num())
		{
			UE_LOG(LogModularCharacterPlugin, Warning,
			       TEXT("Founded skeleton count is %d  Required skeleton count is %d "), SkeletalMeshComponents.Num(),
			       CharacterPartsArray.Num());

			if (CharacterPartsArray.Num() - SkeletalMeshComponents.Num() == 1 && SkeletalMeshComponents.Num() != 0)
			{
				bUseParentSkeletalMeshAsRoot = true;
			}
			else if (CharacterPartsArray.Num() - SkeletalMeshComponents.Num() == -1)
			{
				bUseParentSkeletalMeshAsRoot = false;
			}
			else
			{
				bAutoCreateSkeletalMeshes = true;
			}
		}
	}
	UE_LOG(LogModularCharacterPlugin, Warning,
	       TEXT(
		       "Calculated Best options as : \n bAutoCreateSkeletalMeshes=%s\nbUseParentSkeletalMeshAsRoot=%s\n"
	       ), *FString(bAutoCreateSkeletalMeshes?"True":"False"),
	       *FString(bUseParentSkeletalMeshAsRoot?"True":"False"));
}


void UModularCharacterComponent::SetCharacterPart_Implementation(const FCharacterPart& CharacterPart)
{
	// ApplyCharacterPart(CharacterPart);
	// SyncCharacterParts();
	for (auto i = 0; i < CharacterPartsArray.Num(); i++)
	{
		if (CharacterPartsArray[i].Name.Compare(CharacterPart.Name) == 0)
		{
			CharacterPartsArray.RemoveAt(i);
		}
	}
	// CharacterParts.Remove(CharacterPart);
	CharacterPartsArray.AddUnique(CharacterPart);

	// if game is single player just reinitialize the meshes
	if (const ENetMode NetMode = GetOwner()->GetNetMode(); NetMode == NM_Standalone)
	{
		SyncCharacterParts();
		// InitializeSkeletalMeshes();
		// Server_SetCharacterParts(CharacterPartsArray);
		Internal_ApplyCharacterPart(CharacterPart);
	}
	else if (NetMode == NM_Client)
	{
		Server_SetCharacterParts(CharacterPartsArray);
	}
	else if (NetMode == NM_ListenServer)
	{
		SyncCharacterParts();
		Server_SetCharacterParts_Implementation(CharacterPartsArray);
	}
}

<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>libprotobuf-mutator</Name>
  <!-- Software Name and Version  -->
<!-- Software Name: libprotobuf-mutator
    Download Link: https://github.com/google/libprotobuf-mutator/archive/refs/tags/v1.3.zip
    Version: 
    Note: Dependecy of ONNX Runtime
    -->
<Location>/Engine/Plugins/NNE/NNERuntimeORT/Source/ThirdParty/Onnxruntime/</Location>
<Function>The software is part of the Onnxruntime library, which is used to run neural network inference through the onnx runtime backend and also to optimize ML models.</Function>
<Eula>https://github.com/google/libprotobuf-mutator/blob/v1.3/LICENSE</Eula>
  <RedistributeTo>
    <EndUserGroup>Licencees</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>/Engine/Source/ThirdParty/Licenses</LicenseFolder>
</TpsData>
 


 
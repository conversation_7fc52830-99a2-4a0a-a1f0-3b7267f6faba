/*
  Copyright (c) 2012, Intel Corporation
  All rights reserved.

  Redistribution and use in source and binary forms, with or without
  modification, are permitted provided that the following conditions are
  met:

    * Redistributions of source code must retain the above copyright
      notice, this list of conditions and the following disclaimer.

    * Redistributions in binary form must reproduce the above copyright
      notice, this list of conditions and the following disclaimer in the
      documentation and/or other materials provided with the distribution.

    * Neither the name of Intel Corporation nor the names of its
      contributors may be used to endorse or promote products derived from
      this software without specific prior written permission.


   THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS
   IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED
   TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A
   PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER
   OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
   EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
   PROCUREMENT OF SUBS<PERSON>TUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
   PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
   LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
   NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
   SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
*/

#include <math.h>

#if defined(_WIN32) || defined(_WIN64)
#define WINDOWS
#endif

#ifdef WINDOWS
#define CALLINGCONV /*__vectorcall*/
#else
#define CALLINGCONV
#endif

void CALLINGCONV xyzSumAOS(float *a, int count, float *zeros, float *result) {
    float xsum = 0, ysum = 0, zsum = 0;
    for (int i = 0; i < count; i += 3) {
        xsum += a[i];
        ysum += a[i + 1];
        zsum += a[i + 2];
    }
    result[0] = xsum;
    result[1] = ysum;
    result[2] = zsum;
}

void CALLINGCONV xyzSumSOA(float *a, int count, float *zeros, float *result) {
    float xsum = 0, ysum = 0, zsum = 0;
    for (int i = 0; i < count / 3; ++i) {
        float *p = a + (i >> 3) * 24 + (i & 7);
        xsum += p[0];
        ysum += p[8];
        zsum += p[16];
    }
    result[0] = xsum;
    result[1] = ysum;
    result[2] = zsum;
}

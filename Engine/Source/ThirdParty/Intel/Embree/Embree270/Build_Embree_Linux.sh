#!/bin/bash

# Build instructions:
#
# 1. Run this script
#   ./Build_Embree_Linux.sh
#
# 2. Binaries should be in Build-RelWithDebInfo.x86_64-unknown-linux-gnu directory
#

set -eu

export UE_SDKS_ROOT="${UE_SDKS_ROOT:-/epic}"
export LINUX_MULTIARCH_ROOT="${LINUX_MULTIARCH_ROOT:-${UE_SDKS_ROOT}/HostLinux/Linux_x64/v17_clang-10.0.1-centos7}"

if [[ ! -d "${LINUX_MULTIARCH_ROOT}" ]]; then
    echo ERROR: LINUX_MULTIARCH_ROOT envvar not set
    exit 1
fi

echo "Using compiler at: ${LINUX_MULTIARCH_ROOT}"

SCRIPT_DIR=$(cd "$(dirname "$BASH_SOURCE")" ; pwd)
THIRD_PARTY=$(cd "${SCRIPT_DIR}/../../.." ; pwd)

BuildEmbree()
{
	export PATH="${LINUX_MULTIARCH_ROOT}/x86_64-unknown-linux-gnu/bin:$PATH"
    export ARCH=$1
    export FLAVOR=$2
    local BUILD_DIR=${SCRIPT_DIR}/Build-${FLAVOR}.${ARCH}

    echo "Building ${ARCH}"
    rm -rf ${BUILD_DIR}
    mkdir -p ${BUILD_DIR}

    pushd ${BUILD_DIR}

    set -x
    cmake -G Ninja \
      -DCMAKE_TOOLCHAIN_FILE="/tmp/__cmake_toolchain.cmake" \
      -DCMAKE_MAKE_PROGRAM=$(which ninja) \
      -DCMAKE_BUILD_TYPE=${FLAVOR} \
      -DTBB_INCLUDE_DIR=${THIRD_PARTY}/Intel/TBB/IntelTBB-2019u8/include \
      -DTBB_LIBRARY=${THIRD_PARTY}/Intel/TBB/IntelTBB-2019u8/lib/Linux/libtbb.a \
      -DTBB_LIBRARY_MALLOC=${THIRD_PARTY}/Intel/TBB/IntelTBB-2019u8/lib/Linux/libtbbmalloc.a \
      -DISPC_EXECUTABLE=${THIRD_PARTY}/Intel/ISPC/bin/Linux/ispc \
      -DCOMPILER=CLANG \
      -DENABLE_XEON_PHI_SUPPORT=OFF \
      -DENABLE_TUTORIALS=OFF \
      -DENABLE_ISPC_SUPPORT=ON \
      -DENABLE_STATIC_LIB=OFF \
      ${SCRIPT_DIR}/src
    set +x

# OPTION(RTCORE_RAY_MASK "Enables ray mask support.")
# OPTION(RTCORE_STAT_COUNTERS "Enables statistic counters.")
# OPTION(RTCORE_BACKFACE_CULLING "Enables backface culling.")
# OPTION(RTCORE_INTERSECTION_FILTER "Enables intersection filter callback." ON)
# OPTION(RTCORE_BUFFER_STRIDE "Enables buffer strides." ON)
# OPTION(RTCORE_EXPORT_ALL_SYMBOLS "Lets Embree library export all symbols.")
# OPTION(RTCORE_ENABLE_RAYSTREAM_LOGGER "Enables ray stream logger.")
# OPTION(RTCORE_IGNORE_INVALID_RAYS "Ignores invalid rays." OFF) # FIXME: enable by default?
# OPTION(RTCORE_RAY_PACKETS "Enabled support for ray packets." ON)

    echo
    ninja
    echo

    popd
}

( cat <<_EOF_
  ## autogenerated by ${BASH_SOURCE} script
  SET(LINUX_MULTIARCH_ROOT \$ENV{LINUX_MULTIARCH_ROOT})
  SET(ARCHITECTURE_TRIPLE \$ENV{ARCH})

  message (STATUS "LINUX_MULTIARCH_ROOT is '\${LINUX_MULTIARCH_ROOT}'")
  message (STATUS "ARCHITECTURE_TRIPLE is '\${ARCHITECTURE_TRIPLE}'")

  SET(CMAKE_CROSSCOMPILING TRUE)
  SET(CMAKE_SYSTEM_NAME Linux)
  SET(CMAKE_SYSTEM_VERSION 1)

  # sysroot
  SET(CMAKE_SYSROOT \${LINUX_MULTIARCH_ROOT}/\${ARCHITECTURE_TRIPLE})

  SET(CMAKE_LIBRARY_ARCHITECTURE \${ARCHITECTURE_TRIPLE})

  # specify the cross compiler
  SET(CMAKE_C_COMPILER            \${CMAKE_SYSROOT}/bin/clang)
  SET(CMAKE_C_COMPILER_TARGET     \${ARCHITECTURE_TRIPLE})
  SET(CMAKE_C_FLAGS "-fms-extensions -target      \${ARCHITECTURE_TRIPLE}")

  include_directories("${THIRD_PARTY}/Linux/LibCxx/include")
  include_directories("${THIRD_PARTY}/Linux/LibCxx/include/c++/v1")

  set(CMAKE_LINKER_FLAGS "-stdlib=libc++ -L${THIRD_PARTY}/Linux/LibCxx/lib/Linux/\${ARCHITECTURE_TRIPLE}/ ${THIRD_PARTY}/Linux/LibCxx/lib/Linux/\${ARCHITECTURE_TRIPLE}/libc++.a ${THIRD_PARTY}/Linux/LibCxx/lib/Linux/\${ARCHITECTURE_TRIPLE}/libc++abi.a -lpthread")
  set(CMAKE_EXE_LINKER_FLAGS      "\${CMAKE_LINKER_FLAGS}")
  set(CMAKE_MODULE_LINKER_FLAGS   "\${CMAKE_LINKER_FLAGS}")
  set(CMAKE_SHARED_LINKER_FLAGS   "\${CMAKE_LINKER_FLAGS}")
  #set(CMAKE_STATIC_LINKER_FLAGS   "\${CMAKE_LINKER_FLAGS}")

  SET(CMAKE_CXX_COMPILER          \${CMAKE_SYSROOT}/bin/clang++)
  SET(CMAKE_CXX_COMPILER_TARGET   \${ARCHITECTURE_TRIPLE})
  SET(CMAKE_CXX_FLAGS             "-std=c++1z -fms-extensions")
  # https://stackoverflow.com/questions/25525047/cmake-generator-expression-differentiate-c-c-code
  add_compile_options($<$<COMPILE_LANGUAGE:CXX>:-nostdinc++>)

  SET(CMAKE_ASM_COMPILER          \${CMAKE_SYSROOT}/bin/clang)

  SET(CMAKE_FIND_ROOT_PATH        \${LINUX_MULTIARCH_ROOT})

  # hoping to force it to use ar
  set(CMAKE_FIND_ROOT_PATH_MODE_PROGRAM ONLY)
  set(CMAKE_FIND_ROOT_PATH_MODE_LIBRARY ONLY)
  set(CMAKE_FIND_ROOT_PATH_MODE_INCLUDE ONLY)

_EOF_
) > /tmp/__cmake_toolchain.cmake

if [ "$#" -eq 1 ] && [ "$1" == "-debug" ]; then
	BuildEmbree x86_64-unknown-linux-gnu Debug
else
	BuildEmbree x86_64-unknown-linux-gnu RelWithDebInfo
fi

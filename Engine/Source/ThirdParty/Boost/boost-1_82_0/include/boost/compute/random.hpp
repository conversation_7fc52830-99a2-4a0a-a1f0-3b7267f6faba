//---------------------------------------------------------------------------//
// Copyright (c) 2013 <PERSON> <<EMAIL>>
//
// Distributed under the Boost Software License, Version 1.0
// See accompanying file LICENSE_1_0.txt or copy at
// http://www.boost.org/LICENSE_1_0.txt
//
// See http://boostorg.github.com/compute for more information.
//---------------------------------------------------------------------------//

#ifndef BOOST_COMPUTE_RANDOM_HPP
#define BOOST_COMPUTE_RANDOM_HPP

/// \file
///
/// Meta-header to include all Boost.Compute random headers.

#include <boost/compute/random/bernoulli_distribution.hpp>
#include <boost/compute/random/default_random_engine.hpp>
#include <boost/compute/random/discrete_distribution.hpp>
#include <boost/compute/random/linear_congruential_engine.hpp>
#include <boost/compute/random/mersenne_twister_engine.hpp>
#include <boost/compute/random/threefry_engine.hpp>
#include <boost/compute/random/normal_distribution.hpp>
#include <boost/compute/random/uniform_int_distribution.hpp>
#include <boost/compute/random/uniform_real_distribution.hpp>

#endif // BOOST_COMPUTE_RANDOM_HPP

//
// Copyright (c) 2019 <PERSON> Falco (<EMAIL>)
//
// Distributed under the Boost Software License, Version 1.0. (See accompanying
// file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
//
// Official repository: https://github.com/boostorg/json
//

#ifndef BOOST_JSON_HPP
#define BOOST_JSON_HPP

#include <boost/json/detail/config.hpp>

#include <boost/json/array.hpp>
#include <boost/json/basic_parser.hpp>
#include <boost/json/conversion.hpp>
#include <boost/json/error.hpp>
#include <boost/json/fwd.hpp>
#include <boost/json/kind.hpp>
#include <boost/json/memory_resource.hpp>
#include <boost/json/monotonic_resource.hpp>
#include <boost/json/null_resource.hpp>
#include <boost/json/object.hpp>
#include <boost/json/parse.hpp>
#include <boost/json/parse_options.hpp>
#include <boost/json/parser.hpp>
#include <boost/json/pilfer.hpp>
#include <boost/json/serialize.hpp>
#include <boost/json/serializer.hpp>
#include <boost/json/set_pointer_options.hpp>
#include <boost/json/static_resource.hpp>
#include <boost/json/storage_ptr.hpp>
#include <boost/json/stream_parser.hpp>
#include <boost/json/string.hpp>
#include <boost/json/string_view.hpp>
#include <boost/json/system_error.hpp>
#include <boost/json/value.hpp>
#include <boost/json/value_from.hpp>
#include <boost/json/value_ref.hpp>
#include <boost/json/value_stack.hpp>
#include <boost/json/value_to.hpp>
#include <boost/json/visit.hpp>

// Intentionally excluded
//#include <boost/json/basic_parser_impl.hpp>

#endif

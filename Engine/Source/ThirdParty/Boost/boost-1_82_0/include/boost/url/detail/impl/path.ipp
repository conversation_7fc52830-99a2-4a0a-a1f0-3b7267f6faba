//
// Copyright (c) 2019 <PERSON> (<EMAIL>)
//
// Distributed under the Boost Software License, Version 1.0. (See accompanying
// file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
//
// Official repository: https://github.com/boostorg/url
//

#ifndef BOOST_URL_DETAIL_IMPL_PATH_IPP
#define BOOST_URL_DETAIL_IMPL_PATH_IPP

#include <boost/url/detail/path.hpp>

namespace boost {
namespace urls {
namespace detail {

} // detail
} // url
} // boost

#endif
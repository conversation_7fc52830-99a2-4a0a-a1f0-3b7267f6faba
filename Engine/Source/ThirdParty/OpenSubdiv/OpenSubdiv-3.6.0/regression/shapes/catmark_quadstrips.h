//
//   Copyright 2018 DreamWorks Animation LLC.
//
//   Licensed under the Apache License, Version 2.0 (the "Apache License")
//   with the following modification; you may not use this file except in
//   compliance with the Apache License and the following modification to it:
//   Section 6. Trademarks. is deleted and replaced with:
//
//   6. Trademarks. This License does not grant permission to use the trade
//      names, trademarks, service marks, or product names of the Licensor
//      and its affiliates, except as required to comply with Section 4(c) of
//      the License and to reproduce the content of the NOTICE file.
//
//   You may obtain a copy of the Apache License at
//
//       http://www.apache.org/licenses/LICENSE-2.0
//
//   Unless required by applicable law or agreed to in writing, software
//   distributed under the Apache License with the above modification is
//   distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
//   KIND, either express or implied. See the Apache License for the specific
//   language governing permissions and limitations under the Apache License.
//

static const std::string catmark_quadstrips =
"v -1.375  0.000  1.375\n"
"v -1.125 -0.150  1.375\n"
"v -0.875  0.000  1.375\n"
"v -0.625  0.000  1.375\n"
"v -0.375 -0.150  1.375\n"
"v -0.125 -0.150  1.375\n"
"v  0.125  0.000  1.375\n"
"v  0.375  0.000  1.375\n"
"v  0.625 -0.150  1.375\n"
"v  0.875  0.000  1.375\n"
"v  1.125 -0.150  1.375\n"
"v  1.375  0.000  1.375\n"
"v -1.375  0.000  1.125\n"
"v -1.125 -0.150  1.125\n"
"v -0.875  0.000  1.125\n"
"v -0.625  0.000  1.125\n"
"v -0.375 -0.150  1.125\n"
"v -0.125 -0.150  1.125\n"
"v  0.125  0.000  1.125\n"
"v  0.375  0.000  1.125\n"
"v  0.625 -0.150  1.125\n"
"v  0.875  0.000  1.125\n"
"v  1.125 -0.150  1.125\n"
"v  1.375  0.000  1.125\n"
"v -1.375 -0.150  0.875\n"
"v -1.125 -0.150  0.875\n"
"v -0.875  0.000  0.875\n"
"v -0.375  0.000  0.875\n"
"v -0.125  0.000  0.875\n"
"v  0.375  0.000  0.875\n"
"v  0.625 -0.150  0.875\n"
"v  0.875 -0.150  0.875\n"
"v  1.125  0.000  0.875\n"
"v -1.375 -0.150  0.625\n"
"v -1.125 -0.150  0.625\n"
"v -0.875  0.000  0.625\n"
"v -0.625  0.000  0.625\n"
"v -0.375 -0.150  0.625\n"
"v -0.125 -0.150  0.625\n"
"v  0.125  0.000  0.625\n"
"v  0.375  0.000  0.625\n"
"v  0.625 -0.150  0.625\n"
"v  0.875 -0.150  0.625\n"
"v  1.125  0.000  0.625\n"
"v -1.375  0.000  0.375\n"
"v -1.125  0.000  0.375\n"
"v -0.625  0.000  0.375\n"
"v -0.375 -0.150  0.375\n"
"v -0.125 -0.150  0.375\n"
"v  0.125  0.000  0.375\n"
"v  0.625  0.000  0.375\n"
"v  0.875  0.000  0.375\n"
"v -1.125  0.000  0.125\n"
"v -0.875  0.000  0.125\n"
"v -0.625  0.000  0.125\n"
"v -0.375  0.000  0.125\n"
"v -0.125  0.000  0.125\n"
"v  0.125  0.000  0.125\n"
"v  0.375  0.000  0.125\n"
"v  0.625  0.000  0.125\n"
"v  0.875  0.000  0.125\n"
"v -1.375  0.000 -0.125\n"
"v -1.125 -0.150 -0.125\n"
"v -0.875 -0.150 -0.125\n"
"v -0.625  0.000 -0.125\n"
"v -0.375  0.000 -0.125\n"
"v -0.125  0.000 -0.125\n"
"v  0.125  0.000 -0.125\n"
"v  0.375  0.000 -0.125\n"
"v  0.625 -0.150 -0.125\n"
"v  0.875 -0.150 -0.125\n"
"v  1.125  0.000 -0.125\n"
"v  1.375  0.000 -0.125\n"
"v -1.375  0.000 -0.375\n"
"v -1.125  0.000 -0.375\n"
"v -0.875  0.000 -0.375\n"
"v -0.375  0.000 -0.375\n"
"v -0.125  0.000 -0.375\n"
"v  0.625  0.000 -0.375\n"
"v  0.875  0.000 -0.375\n"
"v  1.125  0.000 -0.375\n"
"v  1.375  0.000 -0.375\n"
"v -1.375  0.000 -0.625\n"
"v -1.125  0.000 -0.625\n"
"v -0.875  0.000 -0.625\n"
"v -0.625  0.000 -0.625\n"
"v -0.375 -0.150 -0.625\n"
"v -0.125 -0.150 -0.625\n"
"v  0.125  0.000 -0.625\n"
"v  0.375  0.000 -0.625\n"
"v  0.625  0.000 -0.625\n"
"v  0.875  0.000 -0.625\n"
"v -1.375  0.000 -0.875\n"
"v -1.125  0.000 -0.875\n"
"v -0.875  0.000 -0.875\n"
"v -0.625  0.000 -0.875\n"
"v -0.375 -0.150 -0.875\n"
"v -0.125 -0.150 -0.875\n"
"v  0.125  0.000 -0.875\n"
"v  0.375  0.000 -0.875\n"
"v  0.625  0.000 -0.875\n"
"v  0.875  0.000 -0.875\n"
"v -1.375 -0.150 -1.125\n"
"v -1.125 -0.150 -1.125\n"
"v -0.875  0.000 -1.125\n"
"v -0.625  0.000 -1.125\n"
"v -0.375  0.000 -1.125\n"
"v -0.125  0.000 -1.125\n"
"v  0.125  0.000 -1.125\n"
"v  0.375  0.000 -1.125\n"
"v  0.625 -0.150 -1.125\n"
"v  0.875 -0.150 -1.125\n"
"v  1.125  0.000 -1.125\n"
"v  1.375  0.000 -1.125\n"
"v -1.375 -0.150 -1.375\n"
"v -1.125 -0.150 -1.375\n"
"v -0.875  0.000 -1.375\n"
"v -0.625  0.000 -1.375\n"
"v -0.375  0.000 -1.375\n"
"v -0.125  0.000 -1.375\n"
"v  0.125  0.000 -1.375\n"
"v  0.375  0.000 -1.375\n"
"v  0.625 -0.150 -1.375\n"
"v  0.875 -0.150 -1.375\n"
"v  1.125  0.000 -1.375\n"
"v  1.375  0.000 -1.375\n"
"\n"
"vt 0.000000 1.000000\n"
"vt 0.090909 1.000000\n"
"vt 0.181818 1.000000\n"
"vt 0.272727 1.000000\n"
"vt 0.363636 1.000000\n"
"vt 0.454545 1.000000\n"
"vt 0.545455 1.000000\n"
"vt 0.636364 1.000000\n"
"vt 0.727273 1.000000\n"
"vt 0.818182 1.000000\n"
"vt 0.909091 1.000000\n"
"vt 1.000000 1.000000\n"
"vt 0.000000 0.909091\n"
"vt 0.090909 0.909091\n"
"vt 0.181818 0.909091\n"
"vt 0.272727 0.909091\n"
"vt 0.363636 0.909091\n"
"vt 0.454545 0.909091\n"
"vt 0.545455 0.909091\n"
"vt 0.636364 0.909091\n"
"vt 0.727273 0.909091\n"
"vt 0.818182 0.909091\n"
"vt 0.909091 0.909091\n"
"vt 1.000000 0.909091\n"
"vt 0.000000 0.818182\n"
"vt 0.090909 0.818182\n"
"vt 0.181818 0.818182\n"
"vt 0.363636 0.818182\n"
"vt 0.454545 0.818182\n"
"vt 0.636364 0.818182\n"
"vt 0.727273 0.818182\n"
"vt 0.818182 0.818182\n"
"vt 0.909091 0.818182\n"
"vt 0.000000 0.727273\n"
"vt 0.090909 0.727273\n"
"vt 0.181818 0.727273\n"
"vt 0.272727 0.727273\n"
"vt 0.363636 0.727273\n"
"vt 0.454545 0.727273\n"
"vt 0.545455 0.727273\n"
"vt 0.636364 0.727273\n"
"vt 0.727273 0.727273\n"
"vt 0.818182 0.727273\n"
"vt 0.909091 0.727273\n"
"vt 0.000000 0.636364\n"
"vt 0.090909 0.636364\n"
"vt 0.272727 0.636364\n"
"vt 0.363636 0.636364\n"
"vt 0.454545 0.636364\n"
"vt 0.545455 0.636364\n"
"vt 0.727273 0.636364\n"
"vt 0.818182 0.636364\n"
"vt 0.090909 0.545455\n"
"vt 0.181818 0.545455\n"
"vt 0.272727 0.545455\n"
"vt 0.363636 0.545455\n"
"vt 0.454545 0.545455\n"
"vt 0.545455 0.545455\n"
"vt 0.636364 0.545455\n"
"vt 0.727273 0.545455\n"
"vt 0.818182 0.545455\n"
"vt 0.000000 0.454545\n"
"vt 0.090909 0.454545\n"
"vt 0.181818 0.454545\n"
"vt 0.272727 0.454545\n"
"vt 0.363636 0.454545\n"
"vt 0.454545 0.454545\n"
"vt 0.545455 0.454545\n"
"vt 0.636364 0.454545\n"
"vt 0.727273 0.454545\n"
"vt 0.818182 0.454545\n"
"vt 0.909091 0.454545\n"
"vt 1.000000 0.454545\n"
"vt 0.000000 0.363636\n"
"vt 0.090909 0.363636\n"
"vt 0.181818 0.363636\n"
"vt 0.363636 0.363636\n"
"vt 0.454545 0.363636\n"
"vt 0.727273 0.363636\n"
"vt 0.818182 0.363636\n"
"vt 0.909091 0.363636\n"
"vt 1.000000 0.363636\n"
"vt 0.000000 0.272727\n"
"vt 0.090909 0.272727\n"
"vt 0.181818 0.272727\n"
"vt 0.272727 0.272727\n"
"vt 0.363636 0.272727\n"
"vt 0.454545 0.272727\n"
"vt 0.545455 0.272727\n"
"vt 0.636364 0.272727\n"
"vt 0.727273 0.272727\n"
"vt 0.818182 0.272727\n"
"vt 0.000000 0.181818\n"
"vt 0.090909 0.181818\n"
"vt 0.181818 0.181818\n"
"vt 0.272727 0.181818\n"
"vt 0.363636 0.181818\n"
"vt 0.454545 0.181818\n"
"vt 0.545455 0.181818\n"
"vt 0.636364 0.181818\n"
"vt 0.727273 0.181818\n"
"vt 0.818182 0.181818\n"
"vt 0.000000 0.090909\n"
"vt 0.090909 0.090909\n"
"vt 0.181818 0.090909\n"
"vt 0.272727 0.090909\n"
"vt 0.363636 0.090909\n"
"vt 0.454545 0.090909\n"
"vt 0.545455 0.090909\n"
"vt 0.636364 0.090909\n"
"vt 0.727273 0.090909\n"
"vt 0.818182 0.090909\n"
"vt 0.909091 0.090909\n"
"vt 1.000000 0.090909\n"
"vt 0.000000 -0.000000\n"
"vt 0.090909 -0.000000\n"
"vt 0.181818 -0.000000\n"
"vt 0.272727 -0.000000\n"
"vt 0.363636 -0.000000\n"
"vt 0.454545 -0.000000\n"
"vt 0.545455 -0.000000\n"
"vt 0.636364 -0.000000\n"
"vt 0.727273 -0.000000\n"
"vt 0.818182 -0.000000\n"
"vt 0.909091 -0.000000\n"
"vt 1.000000 -0.000000\n"
"\n"
"f 13/13 14/14 2/2 1/1\n"
"f 14/14 15/15 3/3 2/2\n"
"f 16/16 17/17 5/5 4/4\n"
"f 17/17 18/18 6/6 5/5\n"
"f 18/18 19/19 7/7 6/6\n"
"f 20/20 21/21 9/9 8/8\n"
"f 21/21 22/22 10/10 9/9\n"
"f 22/22 23/23 11/11 10/10\n"
"f 23/23 24/24 12/12 11/11\n"
"f 34/34 35/35 26/26 25/25\n"
"f 35/35 36/36 27/27 26/26\n"
"f 38/38 39/39 29/29 28/28\n"
"f 41/41 42/42 31/31 30/30\n"
"f 42/42 43/43 32/32 31/31\n"
"f 43/43 44/44 33/33 32/32\n"
"f 45/45 46/46 35/35 34/34\n"
"f 47/47 48/48 38/38 37/37\n"
"f 48/48 49/49 39/39 38/38\n"
"f 49/49 50/50 40/40 39/39\n"
"f 51/51 52/52 43/43 42/42\n"
"f 56/56 57/57 49/49 48/48\n"
"f 63/63 64/64 54/54 53/53\n"
"f 64/64 65/65 55/55 54/54\n"
"f 68/68 69/69 59/59 58/58\n"
"f 69/69 70/70 60/60 59/59\n"
"f 70/70 71/71 61/61 60/60\n"
"f 74/74 75/75 63/63 62/62\n"
"f 75/75 76/76 64/64 63/63\n"
"f 77/77 78/78 67/67 66/66\n"
"f 79/79 80/80 71/71 70/70\n"
"f 80/80 81/81 72/72 71/71\n"
"f 81/81 82/82 73/73 72/72\n"
"f 87/87 88/88 78/78 77/77\n"
"f 93/93 94/94 84/84 83/83\n"
"f 95/95 96/96 86/86 85/85\n"
"f 96/96 97/97 87/87 86/86\n"
"f 97/97 98/98 88/88 87/87\n"
"f 98/98 99/99 89/89 88/88\n"
"f 99/99 100/100 90/90 89/89\n"
"f 101/101 102/102 92/92 91/91\n"
"f 103/103 104/104 94/94 93/93\n"
"f 107/107 108/108 98/98 97/97\n"
"f 111/111 112/112 102/102 101/101\n"
"f 115/115 116/116 104/104 103/103\n"
"f 116/116 117/117 105/105 104/104\n"
"f 117/117 118/118 106/106 105/105\n"
"f 119/119 120/120 108/108 107/107\n"
"f 121/121 122/122 110/110 109/109\n"
"f 122/122 123/123 111/111 110/110\n"
"f 123/123 124/124 112/112 111/111\n"
"f 124/124 125/125 113/113 112/112\n"
"f 125/125 126/126 114/114 113/113\n"
"\n"
"t interpolateboundary 1/0/0 1\n"
"\n"
;

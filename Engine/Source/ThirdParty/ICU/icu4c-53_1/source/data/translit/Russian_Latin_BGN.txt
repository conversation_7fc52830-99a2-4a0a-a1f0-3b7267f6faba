# ***************************************************************************
# *
# *  Copyright (C) 2004-2014, International Business Machines
# *  Corporation; Unicode, Inc.; and others.  All Rights Reserved.
# *
# ***************************************************************************
# File: Russian_Latin_BGN.txt
# Generated from CLDR 
#
::[АБВГДЕЁЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯабвгдеёжзийклмнопрстуфхцчшщъыьэюя];
$prime = ʹ ;
$doublePrime = ʺ ;
$wordBoundary = [^[:L:][:M:][:N:]] ;
$upperConsonants = [БВГДЖЙКЛМНПРСТФХЦЧШЩЭ] ;
$lowerConsonants = [бвгджйклмнпрстфхцчшщэ] ;
$consonants = [$upperConsonants $lowerConsonants] ;
$upperVowels = [АЕЁЭИОУЫЮЯ] ;
$lowerVowels = [аеёэиоуыюя] ;
$vowels = [$upperVowels $lowerVowels] ;
$lower = [$lowerConsonants $lowerVowels] ;
$upper = [$upperConsonants $upperVowels] ;
[$upperVowels [ЙЪЬ]] { Е } $upper → YE ; # CYRILLIC CAPITAL LETTER IE
[$upperVowels [ЙЪЬ]] { Е → Ye ; # CYRILLIC CAPITAL LETTER IE
[$upperVowels $lowerVowels [ЙйЪъЬь]] { е → ye ; # CYRILLIC SMALL LETTER IE
[$upperVowels [ЙЪЬ]] { Ё } $upper → YË ; # CYRILLIC CAPITAL LETTER IO
[$upperVowels [ЙЪЬ]] { Ё → Yë ; # CYRILLIC CAPITAL LETTER IO
[$upperVowels $lowerVowels [ЙйЪъЬь]] { ё → yë ; # CYRILLIC SMALL LETTER IO
::Null;
А → A ; # CYRILLIC CAPITAL LETTER A
а → a ; # CYRILLIC SMALL LETTER A
Б → B ; # CYRILLIC CAPITAL LETTER BE
б → b ; # CYRILLIC SMALL LETTER BE
В → V ; # CYRILLIC CAPITAL LETTER VE
в → v ; # CYRILLIC SMALL LETTER VE
Г → G ; # CYRILLIC CAPITAL LETTER GHE
г → g ; # CYRILLIC SMALL LETTER GHE
Д → D ; # CYRILLIC CAPITAL LETTER DE
д → d ; # CYRILLIC SMALL LETTER DE
$wordBoundary{Е} $upper → YE ; # CYRILLIC CAPITAL LETTER IE
$wordBoundary{Е → Ye ; # CYRILLIC CAPITAL LETTER IE
Е → E ; # CYRILLIC CAPITAL LETTER IE
$wordBoundary{е → ye ; # CYRILLIC SMALL LETTER IE
е → e ; # CYRILLIC SMALL LETTER IE
$wordBoundary {Ё} $upper → YË ; # CYRILLIC CAPITAL LETTER IO
$wordBoundary {Ё} $lower → Yë ; # CYRILLIC CAPITAL LETTER IO
Ё → Ë ; # CYRILLIC CAPITAL LETTER IO
$wordBoundary{ё → yë ; # CYRILLIC SMALL LETTER IO
ё → ë ; # CYRILLIC SMALL LETTER IO
Ж} $lower → Zh ; # CYRILLIC CAPITAL LETTER ZHE
Ж → ZH ; # CYRILLIC CAPITAL LETTER ZHE
ж → zh ; # CYRILLIC SMALL LETTER ZHE
З → Z ; # CYRILLIC CAPITAL LETTER ZE
з → z ; # CYRILLIC SMALL LETTER ZE
И → I ; # CYRILLIC CAPITAL LETTER I
и → i ; # CYRILLIC SMALL LETTER I
Й → Y ; # CYRILLIC CAPITAL LETTER I
й → y ; # CYRILLIC SMALL LETTER I
К → K ; # CYRILLIC CAPITAL LETTER KA
к → k ; # CYRILLIC SMALL LETTER KA
Л → L ; # CYRILLIC CAPITAL LETTER EL
л → l ; # CYRILLIC SMALL LETTER EL
М → M ; # CYRILLIC CAPITAL LETTER EM
м → m ; # CYRILLIC SMALL LETTER EM
Н → N ; # CYRILLIC CAPITAL LETTER EN
н → n ; # CYRILLIC SMALL LETTER EN
О → O ; # CYRILLIC CAPITAL LETTER O
о → o ; # CYRILLIC SMALL LETTER O
П → P ; # CYRILLIC CAPITAL LETTER PE
п → p ; # CYRILLIC SMALL LETTER PE
Р → R ; # CYRILLIC CAPITAL LETTER ER
р → r ; # CYRILLIC SMALL LETTER ER
С → S ; # CYRILLIC CAPITAL LETTER ES
с → s ; # CYRILLIC SMALL LETTER ES
ТС → TS ; # CYRILLIC CAPITAL LETTER TE
Тс → Ts ; # CYRILLIC CAPITAL LETTER TE
тс → ts ; # CYRILLIC SMALL LETTER TE
Т → T ; # CYRILLIC CAPITAL LETTER TE
т → t ; # CYRILLIC SMALL LETTER TE
У → U ; # CYRILLIC CAPITAL LETTER U
у → u ; # CYRILLIC SMALL LETTER U
Ф → F ; # CYRILLIC CAPITAL LETTER EF
ф → f ; # CYRILLIC SMALL LETTER EF
Х} $lower → Kh ; # CYRILLIC CAPITAL LETTER HA
Х → KH ; # CYRILLIC CAPITAL LETTER HA
х → kh ; # CYRILLIC SMALL LETTER HA
Ц} $lower → Ts ; # CYRILLIC CAPITAL LETTER TSE
Ц → TS ; # CYRILLIC CAPITAL LETTER TSE
ц → ts ; # CYRILLIC SMALL LETTER TSE
Ч} $lower → Ch ; # CYRILLIC CAPITAL LETTER CHE
Ч → CH ; # CYRILLIC CAPITAL LETTER CHE
ч → ch ; # CYRILLIC SMALL LETTER CHE
ШЧ → SHCH ; # CYRILLIC CAPITAL LETTER SHA
Шч → Shch ; # CYRILLIC CAPITAL LETTER SHA
шч → shch ; # CYRILLIC SMALL LETTER SHA
Ш} $lower → Sh ; # CYRILLIC CAPITAL LETTER SHA
Ш → SH ; # CYRILLIC CAPITAL LETTER SHA
ш → sh ; # CYRILLIC SMALL LETTER SHA
Щ} $lower → Shch ; # CYRILLIC CAPITAL LETTER SHCHA
Щ → SHCH ; # CYRILLIC CAPITAL LETTER SHCHA
щ → shch ; # CYRILLIC SMALL LETTER SHCHA
Ъ → $doublePrime ; # CYRILLIC CAPITAL LETTER HARD SIGN
ъ → $doublePrime ; # CYRILLIC SMALL LETTER HARD SIGN
Ы → Y ; # CYRILLIC CAPITAL LETTER YERU
ы → y ; # CYRILLIC SMALL LETTER YERU
Ь → $prime ; # CYRILLIC CAPITAL LETTER SOFT SIGN
ь → $prime ; # CYRILLIC SMALL LETTER SOFT SIGN
Э → E ; # CYRILLIC CAPITAL LETTER E
э → e ; # CYRILLIC SMALL LETTER E
Ю} $lower → Yu ; # CYRILLIC CAPITAL LETTER YU
Ю → YU ; # CYRILLIC CAPITAL LETTER YU
ю → yu ; # CYRILLIC SMALL LETTER YU
Я} $lower → Ya ; # CYRILLIC CAPITAL LETTER YA
Я → YA ; # CYRILLIC CAPITAL LETTER YA
я → ya ; # CYRILLIC SMALL LETTER YA

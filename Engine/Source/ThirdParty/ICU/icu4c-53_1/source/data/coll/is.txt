// ***************************************************************************
// *
// * Copyright (C) 2014 International Business Machines
// * Corporation and others. All Rights Reserved.
// * Tool: org.unicode.cldr.icu.NewLdml2IcuConverter
// * Source File: <path>/common/collation/is.xml
// *
// ***************************************************************************
/**
 * ICU <specials> source: <path>/xml/collation/is.xml
 */
is{
    Version{"2.0.96.14"}
    collations{
        search{
            Sequence{
                "[normalization on][suppressContractions [เ-ไ ເ-ໄ ꪵ ꪶ ꪹ ꪻ ꪼ]]"
                "&'='<'≠'"
                "&ا"
                "<<<ﺎ<<<ﺍ"
                "<<آ"
                "<<<ﺂ<<<ﺁ"
                "<<أ"
                "<<<ﺄ<<<ﺃ"
                "<<إ"
                "<<<ﺈ<<<ﺇ"
                "&و"
                "<<<ۥ"
                "<<<ﻮ<<<ﻭ"
                "<<ؤ"
                "<<<ﺆ<<<ﺅ"
                "&ي"
                "<<<ۦ"
                "<<<ﻳ<<<ﻴ<<<ﻲ<<<ﻱ"
                "<<ئ"
                "<<<ﺋ<<<ﺌ<<<ﺊ<<<ﺉ"
                "<<ى"
                "<<<ﯨ<<<ﯩ"
                "<<<ﻰ<<<ﻯ"
                "&ه"
                "<<<ﻫ<<<ﻬ<<<ﻪ<<<ﻩ"
                "<<ة"
                "<<<ﺔ<<<ﺓ"
                "&[last primary ignorable]<<׳"
                "<<״"
                "<<ـ"
                "<<ฺ"
                "&ᄀ"
                "=ᆨ"
                "&ᄀᄀ"
                "=ᄁ=ᆩ"
                "&ᄀᄉ"
                "=ᆪ"
                "&ᄂ"
                "=ᆫ"
                "&ᄂᄌ"
                "=ᆬ"
                "&ᄂᄒ"
                "=ᆭ"
                "&ᄃ"
                "=ᆮ"
                "&ᄃᄃ"
                "=ᄄ"
                "&ᄅ"
                "=ᆯ"
                "&ᄅᄀ"
                "=ᆰ"
                "&ᄅᄆ"
                "=ᆱ"
                "&ᄅᄇ"
                "=ᆲ"
                "&ᄅᄉ"
                "=ᆳ"
                "&ᄅᄐ"
                "=ᆴ"
                "&ᄅᄑ"
                "=ᆵ"
                "&ᄅᄒ"
                "=ᆶ"
                "&ᄆ"
                "=ᆷ"
                "&ᄇ"
                "=ᆸ"
                "&ᄇᄇ"
                "=ᄈ"
                "&ᄇᄉ"
                "=ᆹ"
                "&ᄉ"
                "=ᆺ"
                "&ᄉᄉ"
                "=ᄊ=ᆻ"
                "&ᄋ"
                "=ᆼ"
                "&ᄌ"
                "=ᆽ"
                "&ᄌᄌ"
                "=ᄍ"
                "&ᄎ"
                "=ᆾ"
                "&ᄏ"
                "=ᆿ"
                "&ᄐ"
                "=ᇀ"
                "&ᄑ"
                "=ᇁ"
                "&ᄒ"
                "=ᇂ"
                "&ᅡᅵ"
                "=ᅢ"
                "&ᅣᅵ"
                "=ᅤ"
                "&ᅥᅵ"
                "=ᅦ"
                "&ᅧᅵ"
                "=ᅨ"
                "&ᅩᅡ"
                "=ᅪ"
                "&ᅩᅡᅵ"
                "=ᅫ"
                "&ᅩᅵ"
                "=ᅬ"
                "&ᅮᅴ"
                "=ᅯ"
                "&ᅮᅴᅵ"
                "=ᅰ"
                "&ᅮᅵ"
                "=ᅱ"
                "&[before 1]b<á<<<Á"
                "&d<<đ<<<Đ<ð<<<Ð"
                "&[before 1]f<é<<<É"
                "&[before 1]j<í<<<Í"
                "&[before 1]p<ó<<<Ó"
                "&[before 1]v<ú<<<Ú"
                "&[before 1]z<ý<<<Ý"
                "&[before 1]ǀ<æ<<<Æ<<ä<<<Ä<ö<<<Ö<<ø<<<Ø<å<<<Å"
            }
            Version{"25"}
        }
        standard{
            Sequence{
                "&[before 1]b<á<<<Á"
                "&d<<đ<<<Đ<ð<<<Ð"
                "&[before 1]f<é<<<É"
                "&[before 1]j<í<<<Í"
                "&[before 1]p<ó<<<Ó"
                "&[before 1]v<ú<<<Ú"
                "&[before 1]z<ý<<<Ý"
                "&[before 1]ǀ<æ<<<Æ<<ä<<<Ä<ö<<<Ö<<ø<<<Ø<å<<<Å"
            }
            Version{"25"}
        }
    }
}

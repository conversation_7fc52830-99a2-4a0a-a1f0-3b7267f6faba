// ***************************************************************************
// *
// * Copyright (C) 2014 International Business Machines
// * Corporation and others. All Rights Reserved.
// * Tool: org.unicode.cldr.icu.NewLdml2IcuConverter
// * Source File: <path>/common/main/et.xml
// *
// ***************************************************************************
/**
 * ICU <specials> source: <path>/common/main/et.xml
 */
et{
    AuxExemplarCharacters{"[á à â å ā æ ç é è ê ë ē í ì î ï ī ñ ó ò ŏ ô ø ō œ ú ù û ū]"}
    Ellipsis{
        final{"{0}…"}
        initial{"…{0}"}
        medial{"{0} … {1}"}
        word-final{"{0} …"}
        word-initial{"… {0}"}
        word-medial{"{0} … {1}"}
    }
    ExemplarCharacters{"[a b c d e f g h i j k l m n o p q r s š z ž t u v w õ ä ö ü x y]"}
    ExemplarCharactersIndex{"[A B C D E F G H I J K L M N O P Q R S Š Z Ž T U V Õ Ä Ö Ü X Y]"}
    LocaleScript{
        "Latn",
    }
    MoreInformation{"?"}
    NumberElements{
        latn{
            miscPatterns{
                atLeast{"⩾{0}"}
                range{"{0}‒{1}"}
            }
            patterns{
                accountingFormat{"#,##0.00 ¤;(#,##0.00 ¤)"}
                currencyFormat{"#,##0.00 ¤"}
                decimalFormat{"#,##0.###"}
                percentFormat{"#,##0%"}
                scientificFormat{"#E0"}
            }
            patternsLong{
                decimalFormat{
                    1000{
                        one{"0 tuhat"}
                        other{"0 tuhat"}
                    }
                    10000{
                        one{"00 tuhat"}
                        other{"00 tuhat"}
                    }
                    100000{
                        one{"000 tuhat"}
                        other{"000 tuhat"}
                    }
                    1000000{
                        one{"0 miljon"}
                        other{"0 miljonit"}
                    }
                    ********{
                        one{"00 miljon"}
                        other{"00 miljonit"}
                    }
                    *********{
                        one{"000 miljon"}
                        other{"000 miljonit"}
                    }
                    *********0{
                        one{"0 miljard"}
                        other{"0 miljardit"}
                    }
                    *********00{
                        one{"00 miljard"}
                        other{"00 miljardit"}
                    }
                    *********000{
                        one{"000 miljard"}
                        other{"000 miljardit"}
                    }
                    *********0000{
                        one{"0 triljon"}
                        other{"0 triljonit"}
                    }
                    *********00000{
                        one{"00 triljon"}
                        other{"00 triljonit"}
                    }
                    *********000000{
                        one{"000 triljon"}
                        other{"000 triljonit"}
                    }
                }
            }
            patternsShort{
                decimalFormat{
                    1000{
                        one{"0 tuh"}
                        other{"0 tuh"}
                    }
                    10000{
                        one{"00 tuh"}
                        other{"00 tuh"}
                    }
                    100000{
                        one{"000 tuh"}
                        other{"000 tuh"}
                    }
                    1000000{
                        one{"0 mln"}
                        other{"0 mln"}
                    }
                    ********{
                        one{"00 mln"}
                        other{"00 mln"}
                    }
                    *********{
                        one{"000 mln"}
                        other{"000 mln"}
                    }
                    *********0{
                        one{"0 mld"}
                        other{"0 mld"}
                    }
                    *********00{
                        one{"00 mld"}
                        other{"00 mld"}
                    }
                    *********000{
                        one{"000 mld"}
                        other{"000 mld"}
                    }
                    *********0000{
                        one{"0 trl"}
                        other{"0 trl"}
                    }
                    *********00000{
                        one{"00 trl"}
                        other{"00 trl"}
                    }
                    *********000000{
                        one{"000 trl"}
                        other{"000 trl"}
                    }
                }
            }
            symbols{
                decimal{","}
                exponential{"×10^"}
                group{" "}
                infinity{"∞"}
                list{";"}
                minusSign{"-"}
                nan{"NaN"}
                perMille{"‰"}
                percentSign{"%"}
                plusSign{"+"}
                superscriptingExponent{"×"}
            }
        }
    }
    Version{"*********"}
    calendar{
        generic{
            DateTimePatterns{
                "H:mm.ss zzzz",
                "H:mm.ss z",
                "H:mm.ss",
                "H:mm",
                "EEEE, d. MMMM y G",
                "d. MMMM y G",
                "dd.MM.y G",
                "dd.MM.y GGGGG",
                "{1} {0}",
                "{1} {0}",
                "{1} {0}",
                "{1} {0}",
                "{1} {0}",
            }
            availableFormats{
                Ed{"E, d"}
                Gy{"y G"}
                GyMMM{"MMM y G"}
                GyMMMEd{"E, d. MMMM y G"}
                GyMMMd{"d. MMM y G"}
                H{"HH"}
                Hm{"HH:mm"}
                Hms{"H:mm.ss"}
                M{"M"}
                MEd{"E, d.M"}
                MMM{"MMMM"}
                MMMEd{"E, d. MMM"}
                MMMMEd{"E, d. MMMM"}
                MMMMd{"d. MMMM"}
                MMMd{"d. MMM"}
                Md{"d.M"}
                d{"d"}
                h{"h a"}
                hm{"h:mm a"}
                hms{"h:mm.ss a"}
                mmss{"mm.ss"}
                ms{"mm.ss"}
                y{"y G"}
                yyyy{"y G"}
                yyyyM{"M.y G"}
                yyyyMEd{"E, d.M y G"}
                yyyyMMM{"MMM y G"}
                yyyyMMMEd{"E, d. MMMM y G"}
                yyyyMMMM{"MMMM y G"}
                yyyyMMMd{"d. MMM y G"}
                yyyyMd{"d.M.y G"}
                yyyyQQQ{"QQQ y G"}
                yyyyQQQQ{"QQQQ y G"}
            }
            intervalFormats{
                H{
                    H{"HH–HH"}
                }
                Hm{
                    H{"HH.mm–HH.mm"}
                    m{"HH.mm–HH.mm"}
                }
                Hmv{
                    H{"HH.mm–HH.mm v"}
                    m{"HH:mm–HH:mm v"}
                }
                Hv{
                    H{"HH–HH v"}
                }
                M{
                    M{"M–M"}
                }
                MEd{
                    M{"E, dd.MM–E, dd.MM"}
                    d{"E, dd.MM–E, dd.MM"}
                }
                MMM{
                    M{"LLL–LLL"}
                }
                MMMEd{
                    M{"E, d. MMM–E, d. MMM"}
                    d{"E, d. MMM–E, d. MMM"}
                }
                MMMd{
                    M{"d. MMM–d. MMM"}
                    d{"d.–d. MMM"}
                }
                Md{
                    M{"dd.MM–dd.MM"}
                    d{"dd.MM–dd.MM"}
                }
                d{
                    d{"d–d"}
                }
                fallback{"{0}–{1}"}
                h{
                    a{"h a – h a"}
                    h{"h–h a"}
                }
                hm{
                    a{"h:mm a – h:mm a"}
                    h{"h:mm–h:mm a"}
                    m{"h:mm–h:mm a"}
                }
                hmv{
                    a{"h:mm a – h:mm a v"}
                    h{"h:mm–h:mm a v"}
                    m{"h:mm–h:mm a v"}
                }
                hv{
                    a{"h a – h a v"}
                    h{"h–h a v"}
                }
                y{
                    y{"y–y G"}
                }
                yM{
                    M{"MM.y–MM.y G"}
                    y{"MM.y–MM.y G"}
                }
                yMEd{
                    M{"E, dd.MM.y–E, dd.MM.y G"}
                    d{"E, dd.MM.y–E, dd.MM.y G"}
                    y{"E, dd.MM.y–E, dd.MM.y G"}
                }
                yMMM{
                    M{"MMM–MMM y G"}
                    y{"MMM y–MMM y G"}
                }
                yMMMEd{
                    M{"E, d. MMM–E, d. MMM y G"}
                    d{"E, d. MMM–E, d. MMM y G"}
                    y{"E, d. MMM y–E, d. MMM y G"}
                }
                yMMMM{
                    M{"MMMM–MMMM y G"}
                    y{"MMMM y – MMMM y G"}
                }
                yMMMd{
                    M{"d. MMM–d. MMM y G"}
                    d{"d.–d. MMM y G"}
                    y{"d. MMM y–d. MMM y G"}
                }
                yMd{
                    M{"dd.MM.y–dd.MM.y G"}
                    d{"dd.MM.y–dd.MM.y G"}
                    y{"dd.MM.y–dd.MM.y G"}
                }
            }
        }
        gregorian{
            AmPmMarkers{
                "AM",
                "PM",
            }
            DateTimePatterns{
                "H:mm.ss zzzz",
                "H:mm.ss z",
                "H:mm.ss",
                "H:mm",
                "EEEE, d. MMMM y",
                "d. MMMM y",
                "dd.MM.y",
                "dd.MM.yy",
                "{1} {0}",
                "{1} {0}",
                "{1} {0}",
                "{1} {0}",
                "{1} {0}",
            }
            appendItems{
                Timezone{"{0} {1}"}
            }
            availableFormats{
                EHm{"E HH:mm"}
                EHms{"E HH:mm:ss"}
                Ed{"E, d"}
                Ehm{"E h:mm a"}
                Ehms{"E h:mm:ss a"}
                Gy{"y G"}
                GyMMM{"MMM y G"}
                GyMMMEd{"E, d. MMMM y G"}
                GyMMMd{"d. MMM y G"}
                H{"HH"}
                Hm{"HH:mm"}
                Hms{"H:mm.ss"}
                M{"M"}
                MEd{"E, d.M"}
                MMM{"MMMM"}
                MMMEd{"E, d. MMM"}
                MMMMEd{"E, d. MMMM"}
                MMMMd{"d. MMMM"}
                MMMd{"d. MMM"}
                Md{"d.M"}
                d{"d"}
                h{"h a"}
                hm{"h:mm a"}
                hms{"h:mm.ss a"}
                mmss{"mm.ss"}
                ms{"mm.ss"}
                y{"y"}
                yM{"M.y"}
                yMEd{"E, d.M y"}
                yMMM{"MMM y"}
                yMMMEd{"E, d. MMMM y"}
                yMMMM{"MMMM y"}
                yMMMd{"d. MMM y"}
                yMd{"d.M.y"}
                yQQQ{"QQQ y"}
                yQQQQ{"QQQQ y"}
            }
            dayNames{
                format{
                    abbreviated{
                        "P",
                        "E",
                        "T",
                        "K",
                        "N",
                        "R",
                        "L",
                    }
                    narrow{
                        "P",
                        "E",
                        "T",
                        "K",
                        "N",
                        "R",
                        "L",
                    }
                    short{
                        "P",
                        "E",
                        "T",
                        "K",
                        "N",
                        "R",
                        "L",
                    }
                    wide{
                        "pühapäev",
                        "esmaspäev",
                        "teisipäev",
                        "kolmapäev",
                        "neljapäev",
                        "reede",
                        "laupäev",
                    }
                }
                stand-alone{
                    abbreviated{
                        "P",
                        "E",
                        "T",
                        "K",
                        "N",
                        "R",
                        "L",
                    }
                    narrow{
                        "P",
                        "E",
                        "T",
                        "K",
                        "N",
                        "R",
                        "L",
                    }
                    short{
                        "P",
                        "E",
                        "T",
                        "K",
                        "N",
                        "R",
                        "L",
                    }
                    wide{
                        "pühapäev",
                        "esmaspäev",
                        "teisipäev",
                        "kolmapäev",
                        "neljapäev",
                        "reede",
                        "laupäev",
                    }
                }
            }
            eras{
                abbreviated{
                    "e.m.a.",
                    "m.a.j.",
                }
                narrow{
                    "e.m.a.",
                    "m.a.j.",
                }
                wide{
                    "enne meie aega",
                    "meie aja järgi",
                }
            }
            intervalFormats{
                H{
                    H{"HH–HH"}
                }
                Hm{
                    H{"HH.mm–HH.mm"}
                    m{"HH.mm–HH.mm"}
                }
                Hmv{
                    H{"HH.mm–HH.mm v"}
                    m{"HH:mm–HH:mm v"}
                }
                Hv{
                    H{"HH–HH v"}
                }
                M{
                    M{"MM–MM"}
                }
                MEd{
                    M{"E, dd.MM–E, dd.MM"}
                    d{"E, dd.MM–E, dd.MM"}
                }
                MMM{
                    M{"LLL–LLL"}
                }
                MMMEd{
                    M{"E, d. MMM–E, d. MMM"}
                    d{"E, d. MMM–E, d. MMM"}
                }
                MMMd{
                    M{"d. MMM–d. MMM"}
                    d{"d.–d. MMM"}
                }
                Md{
                    M{"dd.MM–dd.MM"}
                    d{"dd.MM–dd.MM"}
                }
                d{
                    d{"d–d"}
                }
                fallback{"{0}–{1}"}
                h{
                    a{"h a – h a"}
                    h{"h–h a"}
                }
                hm{
                    a{"h:mm a – h:mm a"}
                    h{"h:mm–h:mm a"}
                    m{"h:mm–h:mm a"}
                }
                hmv{
                    a{"h:mm a – h:mm a v"}
                    h{"h:mm–h:mm a v"}
                    m{"h:mm–h:mm a v"}
                }
                hv{
                    a{"h a – h a v"}
                    h{"h–h a v"}
                }
                y{
                    y{"y–y"}
                }
                yM{
                    M{"MM.y–MM.y"}
                    y{"MM.y–MM.y"}
                }
                yMEd{
                    M{"E, dd.MM.y–E, dd.MM.y"}
                    d{"E, dd.MM.y–E, dd.MM.y"}
                    y{"E, dd.MM.y–E, dd.MM.y"}
                }
                yMMM{
                    M{"MMM–MMM y"}
                    y{"MMM y–MMM y"}
                }
                yMMMEd{
                    M{"E, d. MMM–E, d. MMM y"}
                    d{"E, d. MMM–E, d. MMM y"}
                    y{"E, d. MMM y–E, d. MMM y"}
                }
                yMMMM{
                    M{"MMMM–MMMM y"}
                    y{"MMMM y – MMMM y"}
                }
                yMMMd{
                    M{"d. MMM–d. MMM y"}
                    d{"d.–d. MMM y"}
                    y{"d. MMM y–d. MMM y"}
                }
                yMd{
                    M{"dd.MM.y–dd.MM.y"}
                    d{"dd.MM.y–dd.MM.y"}
                    y{"dd.MM.y–dd.MM.y"}
                }
            }
            monthNames{
                format{
                    abbreviated{
                        "jaan",
                        "veebr",
                        "märts",
                        "apr",
                        "mai",
                        "juuni",
                        "juuli",
                        "aug",
                        "sept",
                        "okt",
                        "nov",
                        "dets",
                    }
                    narrow{
                        "J",
                        "V",
                        "M",
                        "A",
                        "M",
                        "J",
                        "J",
                        "A",
                        "S",
                        "O",
                        "N",
                        "D",
                    }
                    wide{
                        "jaanuar",
                        "veebruar",
                        "märts",
                        "aprill",
                        "mai",
                        "juuni",
                        "juuli",
                        "august",
                        "september",
                        "oktoober",
                        "november",
                        "detsember",
                    }
                }
                stand-alone{
                    abbreviated{
                        "jaan",
                        "veebr",
                        "märts",
                        "apr",
                        "mai",
                        "juuni",
                        "juuli",
                        "aug",
                        "sept",
                        "okt",
                        "nov",
                        "dets",
                    }
                    narrow{
                        "J",
                        "V",
                        "M",
                        "A",
                        "M",
                        "J",
                        "J",
                        "A",
                        "S",
                        "O",
                        "N",
                        "D",
                    }
                    wide{
                        "jaanuar",
                        "veebruar",
                        "märts",
                        "aprill",
                        "mai",
                        "juuni",
                        "juuli",
                        "august",
                        "september",
                        "oktoober",
                        "november",
                        "detsember",
                    }
                }
            }
            quarters{
                format{
                    abbreviated{
                        "K1",
                        "K2",
                        "K3",
                        "K4",
                    }
                    narrow{
                        "1",
                        "2",
                        "3",
                        "4",
                    }
                    wide{
                        "1. kvartal",
                        "2. kvartal",
                        "3. kvartal",
                        "4. kvartal",
                    }
                }
                stand-alone{
                    abbreviated{
                        "K1",
                        "K2",
                        "K3",
                        "K4",
                    }
                    narrow{
                        "1.",
                        "2.",
                        "3.",
                        "4.",
                    }
                    wide{
                        "1. kvartal",
                        "2. kvartal",
                        "3. kvartal",
                        "4. kvartal",
                    }
                }
            }
        }
    }
    delimiters{
        alternateQuotationEnd{"‘"}
        alternateQuotationStart{"‚"}
        quotationEnd{"“"}
        quotationStart{"„"}
    }
    durationUnits{
        hm{"h:mm"}
        hms{"h:mm:ss"}
        ms{"m:ss"}
    }
    fields{
        day{
            dn{"päev"}
            relative{
                "-1"{"eile"}
                "-2"{"üleeile"}
                "0"{"täna"}
                "1"{"homme"}
                "2"{"ülehomme"}
            }
            relativeTime{
                future{
                    one{"{0} päeva pärast"}
                    other{"{0} päeva pärast"}
                }
                past{
                    one{"{0} päeva eest"}
                    other{"{0} päeva eest"}
                }
            }
        }
        dayperiod{
            dn{"enne/pärast lõunat"}
        }
        era{
            dn{"ajastu"}
        }
        fri{
            relative{
                "-1"{"eelmine reede"}
                "0"{"käesolev reede"}
                "1"{"järgmine reede"}
            }
        }
        hour{
            dn{"tund"}
            relativeTime{
                future{
                    one{"{0} tunni pärast"}
                    other{"{0} tunni pärast"}
                }
                past{
                    one{"{0} tunni eest"}
                    other{"{0} tunni eest"}
                }
            }
        }
        minute{
            dn{"minut"}
            relativeTime{
                future{
                    one{"{0} minuti pärast"}
                    other{"{0} minuti pärast"}
                }
                past{
                    one{"{0} minuti eest"}
                    other{"{0} minuti eest"}
                }
            }
        }
        mon{
            relative{
                "-1"{"eelmine esmaspäev"}
                "0"{"käesolev esmaspäev"}
                "1"{"järgmine esmaspäev"}
            }
        }
        month{
            dn{"kuu"}
            relative{
                "-1"{"eelmine kuu"}
                "0"{"käesolev kuu"}
                "1"{"järgmine kuu"}
            }
            relativeTime{
                future{
                    one{"{0} kuu pärast"}
                    other{"{0} kuu pärast"}
                }
                past{
                    one{"{0} kuu eest"}
                    other{"{0} kuu eest"}
                }
            }
        }
        sat{
            relative{
                "-1"{"eelmine laupäev"}
                "0"{"käesolev laupäev"}
                "1"{"järgmine laupäev"}
            }
        }
        second{
            dn{"sekund"}
            relative{
                "0"{"nüüd"}
            }
            relativeTime{
                future{
                    one{"{0} sekundi pärast"}
                    other{"{0} sekundi pärast"}
                }
                past{
                    one{"{0} sekundi eest"}
                    other{"{0} sekundi eest"}
                }
            }
        }
        sun{
            relative{
                "-1"{"eelmine pühapäev"}
                "0"{"käesolev pühapäev"}
                "1"{"järgmine pühapäev"}
            }
        }
        thu{
            relative{
                "-1"{"eelmine neljapäev"}
                "0"{"käesolev neljapäev"}
                "1"{"järgmine neljapäev"}
            }
        }
        tue{
            relative{
                "-1"{"eelmine teisipäev"}
                "0"{"käesolev teisipäev"}
                "1"{"järgmine teisipäev"}
            }
        }
        wed{
            relative{
                "-1"{"eelmine kolmapäev"}
                "0"{"käesolev kolmapäev"}
                "1"{"järgmine kolmapäev"}
            }
        }
        week{
            dn{"nädal"}
            relative{
                "-1"{"eelmine nädal"}
                "0"{"käesolev nädal"}
                "1"{"järgmine nädal"}
            }
            relativeTime{
                future{
                    one{"{0} nädala pärast"}
                    other{"{0} nädala pärast"}
                }
                past{
                    one{"{0} nädala eest"}
                    other{"{0} nädala eest"}
                }
            }
        }
        weekday{
            dn{"nädalapäev"}
        }
        year{
            dn{"aasta"}
            relative{
                "-1"{"eelmine aasta"}
                "0"{"käesolev aasta"}
                "1"{"järgmine aasta"}
            }
            relativeTime{
                future{
                    one{"{0} aasta pärast"}
                    other{"{0} aasta pärast"}
                }
                past{
                    one{"{0} aasta eest"}
                    other{"{0} aasta eest"}
                }
            }
        }
        zone{
            dn{"ajavöönd"}
        }
    }
    listPattern{
        standard{
            2{"{0} ja {1}"}
            end{"{0} ja {1}"}
            middle{"{0}, {1}"}
            start{"{0}, {1}"}
        }
        unit{
            2{"{0}, {1}"}
            end{"{0}, {1}"}
            middle{"{0}, {1}"}
            start{"{0}, {1}"}
        }
        unit-narrow{
            2{"{0} {1}"}
            end{"{0} {1}"}
            middle{"{0} {1}"}
            start{"{0} {1}"}
        }
        unit-short{
            2{"{0}, {1}"}
            end{"{0}, {1}"}
            middle{"{0}, {1}"}
            start{"{0}, {1}"}
        }
    }
    measurementSystemNames{
        UK{"inglise mõõdustik"}
        US{"inglise mõõdustik"}
        metric{"meetermõõdustik"}
    }
    transformNames{
        BGN{"BGN"}
        Numeric{"Numbriline"}
        Tone{"Toon"}
        UNGEGN{"UNGEGN"}
        x-Accents{"Aktsendid"}
        x-Fullwidth{"Täislai"}
        x-Halfwidth{"Poolaius"}
        x-Jamo{"Jamo"}
        x-Pinyin{"Pinyin"}
        x-Publishing{"Kirjastamine"}
    }
    units{
        acceleration{
            g-force{
                one{"{0} maa gravitatsiooni"}
                other{"{0} maa gravitatsiooni"}
            }
        }
        angle{
            arc-minute{
                one{"{0} kaareminut"}
                other{"{0} kaareminut"}
            }
            arc-second{
                one{"{0} kaaresekund"}
                other{"{0} kaaresekund"}
            }
            degree{
                one{"{0} kraad"}
                other{"{0} kraadi"}
            }
        }
        area{
            acre{
                one{"{0} aaker"}
                other{"{0} aakrit"}
            }
            hectare{
                one{"{0} hektar"}
                other{"{0} hektarit"}
            }
            square-foot{
                one{"{0} ruutjalg"}
                other{"{0} ruutjalga"}
            }
            square-kilometer{
                one{"{0} ruutkilomeeter"}
                other{"{0} ruutkilomeetrit"}
            }
            square-meter{
                one{"{0} ruutmeeter"}
                other{"{0} ruutmeetrit"}
            }
            square-mile{
                one{"{0} ruutmiil"}
                other{"{0} ruutmiili"}
            }
        }
        compound{
            per{"{0} {1} kohta"}
        }
        duration{
            day{
                one{"{0} ööpäev"}
                other{"{0} ööpäeva"}
            }
            hour{
                one{"{0} tund"}
                other{"{0} tundi"}
            }
            millisecond{
                one{"{0} millisekund"}
                other{"{0} millisekundit"}
            }
            minute{
                one{"{0} minut"}
                other{"{0} minutit"}
            }
            month{
                one{"{0} kuu"}
                other{"{0} kuud"}
            }
            second{
                one{"{0} sekund"}
                other{"{0} sekundit"}
            }
            week{
                one{"{0} nädal"}
                other{"{0} nädalat"}
            }
            year{
                one{"{0} aasta"}
                other{"{0} aastat"}
            }
        }
        length{
            centimeter{
                one{"{0} sentimeeter"}
                other{"{0} sentimeetrit"}
            }
            foot{
                one{"{0} jalg"}
                other{"{0} jalga"}
            }
            inch{
                one{"{0} toll"}
                other{"{0} tolli"}
            }
            kilometer{
                one{"{0} kilomeeter"}
                other{"{0} kilomeetrit"}
            }
            light-year{
                one{"{0} valgusaasta"}
                other{"{0} valgusaastat"}
            }
            meter{
                one{"{0} meeter"}
                other{"{0} meetrit"}
            }
            mile{
                one{"{0} miil"}
                other{"{0} miili"}
            }
            millimeter{
                one{"{0} millimeeter"}
                other{"{0} millimeetrit"}
            }
            picometer{
                one{"{0} pikomeeter"}
                other{"{0} pikomeetrit"}
            }
            yard{
                one{"{0} jard"}
                other{"{0} jardi"}
            }
        }
        mass{
            gram{
                one{"{0} gramm"}
                other{"{0} grammi"}
            }
            kilogram{
                one{"{0} kilogramm"}
                other{"{0} kilogrammi"}
            }
            ounce{
                one{"{0} unts"}
                other{"{0} untsi"}
            }
            pound{
                one{"{0} nael"}
                other{"{0} naela"}
            }
        }
        power{
            horsepower{
                one{"{0} hobujõud"}
                other{"{0} hobujõudu"}
            }
            kilowatt{
                one{"{0} kilovatt"}
                other{"{0} kilovatti"}
            }
            watt{
                one{"{0} vatt"}
                other{"{0} vatti"}
            }
        }
        pressure{
            hectopascal{
                one{"{0} hektopaskal"}
                other{"{0} hektopaskalit"}
            }
            inch-hg{
                one{"{0} toll elavhõbedasammast"}
                other{"{0} tolli elavhõbedasammast"}
            }
            millibar{
                one{"{0} millibaar"}
                other{"{0} millibaari"}
            }
        }
        speed{
            kilometer-per-hour{
                one{"{0} kilomeeter tunnis"}
                other{"{0} kilomeetrit tunnis"}
            }
            meter-per-second{
                one{"{0} meeter sekundis"}
                other{"{0} meetrit sekundis"}
            }
            mile-per-hour{
                one{"{0} miil tunnis"}
                other{"{0} miili tunnis"}
            }
        }
        temperature{
            celsius{
                one{"{0} Celsiuse kraad"}
                other{"{0} Celsiuse kraadi"}
            }
            fahrenheit{
                one{"{0} Fahrenheiti kraad"}
                other{"{0} Fahrenheiti kraadi"}
            }
        }
        volume{
            cubic-kilometer{
                one{"{0} kuupkilomeeter"}
                other{"{0} kuupkilomeetrit"}
            }
            cubic-mile{
                one{"{0} kuupmiil"}
                other{"{0} kuupmiili"}
            }
            liter{
                one{"{0} liiter"}
                other{"{0} liitrit"}
            }
        }
    }
    unitsNarrow{
        acceleration{
            g-force{
                one{"{0}G"}
                other{"{0}G"}
            }
        }
        angle{
            arc-minute{
                one{"{0}′"}
                other{"{0}′"}
            }
            arc-second{
                one{"{0}″"}
                other{"{0}″"}
            }
            degree{
                one{"{0}°"}
                other{"{0}°"}
            }
        }
        area{
            acre{
                one{"{0} aaker"}
                other{"{0} aakrit"}
            }
            hectare{
                one{"{0}ha"}
                other{"{0}ha"}
            }
            square-foot{
                one{"{0} ft²"}
                other{"{0} ft²"}
            }
            square-kilometer{
                one{"{0}km²"}
                other{"{0}km²"}
            }
            square-meter{
                one{"{0}m²"}
                other{"{0}m²"}
            }
            square-mile{
                one{"{0} mi²"}
                other{"{0} mi²"}
            }
        }
        compound{
            per{"{0}/{1}"}
        }
        duration{
            day{
                one{"{0} p"}
                other{"{0} p"}
            }
            hour{
                one{"{0} h"}
                other{"{0} h"}
            }
            millisecond{
                one{"{0}ms"}
                other{"{0}ms"}
            }
            minute{
                one{"{0} min"}
                other{"{0} min"}
            }
            month{
                one{"{0} k"}
                other{"{0} k"}
            }
            second{
                one{"{0} s"}
                other{"{0} s"}
            }
            week{
                one{"{0} n"}
                other{"{0} n"}
            }
            year{
                one{"{0} a"}
                other{"{0} a"}
            }
        }
        length{
            centimeter{
                one{"{0} cm"}
                other{"{0} cm"}
            }
            foot{
                one{"{0} jalg"}
                other{"{0} jalga"}
            }
            inch{
                one{"{0} toll"}
                other{"{0} tolli"}
            }
            kilometer{
                one{"{0} km"}
                other{"{0} km"}
            }
            light-year{
                one{"{0} valgusa."}
                other{"{0} valgusa."}
            }
            meter{
                one{"{0} m"}
                other{"{0} m"}
            }
            mile{
                one{"{0} miil"}
                other{"{0} miili"}
            }
            millimeter{
                one{"{0} mm"}
                other{"{0} mm"}
            }
            picometer{
                one{"{0} pm"}
                other{"{0} pm"}
            }
            yard{
                one{"{0} jard"}
                other{"{0} jardi"}
            }
        }
        mass{
            gram{
                one{"{0} g"}
                other{"{0} g"}
            }
            kilogram{
                one{"{0} kg"}
                other{"{0} kg"}
            }
            ounce{
                one{"{0} oz"}
                other{"{0} oz"}
            }
            pound{
                one{"{0} lb"}
                other{"{0} lb"}
            }
        }
        power{
            horsepower{
                one{"{0} hj"}
                other{"{0} hj"}
            }
            kilowatt{
                one{"{0}kW"}
                other{"{0}kW"}
            }
            watt{
                one{"{0}W"}
                other{"{0}W"}
            }
        }
        pressure{
            hectopascal{
                one{"{0} hPa"}
                other{"{0} hPa"}
            }
            inch-hg{
                one{"{0} toll Hg"}
                other{"{0} tolli Hg"}
            }
            millibar{
                one{"{0} mbar"}
                other{"{0} mbar"}
            }
        }
        speed{
            kilometer-per-hour{
                one{"{0}km/h"}
                other{"{0}km/h"}
            }
            meter-per-second{
                one{"{0}m/s"}
                other{"{0}m/s"}
            }
            mile-per-hour{
                one{"{0} mi/h"}
                other{"{0} mi/h"}
            }
        }
        temperature{
            celsius{
                one{"{0} °"}
                other{"{0} °"}
            }
            fahrenheit{
                one{"{0} °F"}
                other{"{0} °F"}
            }
        }
        volume{
            cubic-kilometer{
                one{"{0} km³"}
                other{"{0} km³"}
            }
            cubic-mile{
                one{"{0} mi³"}
                other{"{0} mi³"}
            }
            liter{
                one{"{0} l"}
                other{"{0} l"}
            }
        }
    }
    unitsShort{
        acceleration{
            g-force{
                one{"{0} G"}
                other{"{0} G"}
            }
        }
        angle{
            arc-minute{
                one{"{0}′"}
                other{"{0}′"}
            }
            arc-second{
                one{"{0}″"}
                other{"{0}″"}
            }
            degree{
                one{"{0}°"}
                other{"{0}°"}
            }
        }
        area{
            acre{
                one{"{0} aaker"}
                other{"{0} aakrit"}
            }
            hectare{
                one{"{0} ha"}
                other{"{0} ha"}
            }
            square-foot{
                one{"{0} ft²"}
                other{"{0} ft²"}
            }
            square-kilometer{
                one{"{0} km²"}
                other{"{0} km²"}
            }
            square-meter{
                one{"{0} m²"}
                other{"{0} m²"}
            }
            square-mile{
                one{"{0} mi²"}
                other{"{0} mi²"}
            }
        }
        compound{
            per{"{0}/{1}"}
        }
        duration{
            day{
                one{"{0} päev"}
                other{"{0} päeva"}
            }
            hour{
                one{"{0} h"}
                other{"{0} h"}
            }
            millisecond{
                one{"{0} ms"}
                other{"{0} ms"}
            }
            minute{
                one{"{0} min"}
                other{"{0} min"}
            }
            month{
                one{"{0} kuu"}
                other{"{0} kuud"}
            }
            second{
                one{"{0} s"}
                other{"{0} s"}
            }
            week{
                one{"{0} nädal"}
                other{"{0} nädalat"}
            }
            year{
                one{"{0} a"}
                other{"{0} a"}
            }
        }
        length{
            centimeter{
                one{"{0} cm"}
                other{"{0} cm"}
            }
            foot{
                one{"{0} jalg"}
                other{"{0} jalga"}
            }
            inch{
                one{"{0} toll"}
                other{"{0} tolli"}
            }
            kilometer{
                one{"{0} km"}
                other{"{0} km"}
            }
            light-year{
                one{"{0} valgusa."}
                other{"{0} valgusa."}
            }
            meter{
                one{"{0} m"}
                other{"{0} m"}
            }
            mile{
                one{"{0} miil"}
                other{"{0} miili"}
            }
            millimeter{
                one{"{0} mm"}
                other{"{0} mm"}
            }
            picometer{
                one{"{0} pm"}
                other{"{0} pm"}
            }
            yard{
                one{"{0} jard"}
                other{"{0} jardi"}
            }
        }
        mass{
            gram{
                one{"{0} g"}
                other{"{0} g"}
            }
            kilogram{
                one{"{0} kg"}
                other{"{0} kg"}
            }
            ounce{
                one{"{0} oz"}
                other{"{0} oz"}
            }
            pound{
                one{"{0} lb"}
                other{"{0} lb"}
            }
        }
        power{
            horsepower{
                one{"{0} hj"}
                other{"{0} hj"}
            }
            kilowatt{
                one{"{0} kW"}
                other{"{0} kW"}
            }
            watt{
                one{"{0} W"}
                other{"{0} W"}
            }
        }
        pressure{
            hectopascal{
                one{"{0} hPa"}
                other{"{0} hPa"}
            }
            inch-hg{
                one{"{0} toll Hg"}
                other{"{0} tolli Hg"}
            }
            millibar{
                one{"{0} mbar"}
                other{"{0} mbar"}
            }
        }
        speed{
            kilometer-per-hour{
                one{"{0} km/h"}
                other{"{0} km/h"}
            }
            meter-per-second{
                one{"{0} m/s"}
                other{"{0} m/s"}
            }
            mile-per-hour{
                one{"{0} mi/h"}
                other{"{0} mi/h"}
            }
        }
        temperature{
            celsius{
                one{"{0} °C"}
                other{"{0} °C"}
            }
            fahrenheit{
                one{"{0} °F"}
                other{"{0} °F"}
            }
        }
        volume{
            cubic-kilometer{
                one{"{0} km³"}
                other{"{0} km³"}
            }
            cubic-mile{
                one{"{0} mi³"}
                other{"{0} mi³"}
            }
            liter{
                one{"{0} l"}
                other{"{0} l"}
            }
        }
    }
}

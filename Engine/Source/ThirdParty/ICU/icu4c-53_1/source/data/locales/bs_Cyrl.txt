// ***************************************************************************
// *
// * Copyright (C) 2014 International Business Machines
// * Corporation and others. All Rights Reserved.
// * Tool: org.unicode.cldr.icu.NewLdml2IcuConverter
// * Source File: <path>/common/main/bs_Cyrl.xml
// *
// ***************************************************************************
/**
 * ICU <specials> source: <path>/common/main/bs_Cyrl.xml
 */
bs_Cyrl{
    %%Parent{"root"}
    AuxExemplarCharacters{"[]"}
    Ellipsis{
        final{"{0}…"}
        initial{"…{0}"}
    }
    ExemplarCharacters{"[а б в г д ђ е ж з и ј к л љ м н њ о п р с т ћ у ф х ц ч џ ш]"}
    ExemplarCharactersIndex{"[А Б В Г Д Ђ Е Ж З И Ј К Л Љ М Н Њ О П Р С Т Ћ У Ф Х Ц Ч Џ Ш]"}
    LocaleScript{
        "Cyrl",
    }
    NumberElements{
        latn{
            patterns{
                currencyFormat{"#,##0.00 ¤"}
                decimalFormat{"#,##0.###"}
                percentFormat{"#,##0%"}
                scientificFormat{"#E0"}
            }
            patternsLong{
                decimalFormat{
                    1000{
                        few{"0"}
                        many{"0"}
                        one{"0"}
                        other{"0"}
                    }
                    10000{
                        few{"00 хиљ"}
                        many{"00 хиљ"}
                        one{"00 хиљ"}
                        other{"00 хиљ"}
                    }
                    100000{
                        few{"000 хиљ"}
                        many{"000 хиљ"}
                        one{"000 хиљ"}
                        other{"000 хиљ"}
                    }
                    1000000{
                        few{"0 мил"}
                        many{"0 мил"}
                        one{"0 мил"}
                        other{"0 мил"}
                    }
                    10000000{
                        few{"00 мил"}
                        many{"00 мил"}
                        one{"00 мил"}
                        other{"00 мил"}
                    }
                    100000000{
                        few{"000 мил"}
                        many{"000 мил"}
                        one{"000 мил"}
                        other{"000 мил"}
                    }
                    1000000000{
                        few{"0 млрд"}
                        many{"0 млрд"}
                        one{"0 млрд"}
                        other{"0 млрд"}
                    }
                    10000000000{
                        few{"00 млрд"}
                        many{"00 млрд"}
                        one{"00 млрд"}
                        other{"00 млрд"}
                    }
                    100000000000{
                        few{"000 млрд"}
                        many{"000 млрд"}
                        one{"000 млрд"}
                        other{"000 млрд"}
                    }
                    1000000000000{
                        few{"0 бил"}
                        many{"0 бил"}
                        one{"0 бил"}
                        other{"0 бил"}
                    }
                    10000000000000{
                        few{"00 бил"}
                        many{"00 бил"}
                        one{"00 бил"}
                        other{"00 бил"}
                    }
                    100000000000000{
                        few{"000 бил"}
                        many{"000 бил"}
                        one{"000 бил"}
                        other{"000 бил"}
                    }
                }
            }
            patternsShort{
                decimalFormat{
                    1000{
                        few{"0"}
                        many{"0"}
                        one{"0"}
                        other{"0"}
                    }
                    10000{
                        few{"00 хиљ"}
                        many{"00 хиљ"}
                        one{"00 хиљ"}
                        other{"00 хиљ"}
                    }
                    100000{
                        few{"000 хиљ"}
                        many{"000 хиљ"}
                        one{"000 хиљ"}
                        other{"000 хиљ"}
                    }
                    1000000{
                        few{"0 мил"}
                        many{"0 мил"}
                        one{"0 мил"}
                        other{"0 мил"}
                    }
                    10000000{
                        few{"00 мил"}
                        many{"00 мил"}
                        one{"00 мил"}
                        other{"00 мил"}
                    }
                    100000000{
                        few{"000 мил"}
                        many{"000 мил"}
                        one{"000 мил"}
                        other{"000 мил"}
                    }
                    1000000000{
                        few{"0 млрд"}
                        many{"0 млрд"}
                        one{"0 млрд"}
                        other{"0 млрд"}
                    }
                    10000000000{
                        few{"00 млрд"}
                        many{"00 млрд"}
                        one{"00 млрд"}
                        other{"00 млрд"}
                    }
                    100000000000{
                        few{"000 млрд"}
                        many{"000 млрд"}
                        one{"000 млрд"}
                        other{"000 млрд"}
                    }
                    1000000000000{
                        few{"0 бил"}
                        many{"0 бил"}
                        one{"0 бил"}
                        other{"0 бил"}
                    }
                    10000000000000{
                        few{"00 бил"}
                        many{"00 бил"}
                        one{"00 бил"}
                        other{"00 бил"}
                    }
                    100000000000000{
                        few{"000 бил"}
                        many{"000 бил"}
                        one{"000 бил"}
                        other{"000 бил"}
                    }
                }
            }
            symbols{
                decimal{","}
                exponential{"E"}
                group{"."}
                infinity{"∞"}
                list{";"}
                minusSign{"-"}
                nan{"NaN"}
                perMille{"‰"}
                percentSign{"%"}
                plusSign{"+"}
            }
        }
    }
    Version{"2.0.98.52"}
    calendar{
        buddhist{
            eras{
                abbreviated{
                    "БЕ",
                }
            }
        }
        coptic{
            monthNames{
                format{
                    wide{
                        "Таут",
                        "Баба",
                        "Хатор",
                        "Киахк",
                        "Тоба",
                        "Амшир",
                        "Барамхат",
                        "Барамуда",
                        "Башанс",
                        "Паона",
                        "Епеп",
                        "Месра",
                        "Наси",
                    }
                }
            }
        }
        ethiopic{
            monthNames{
                format{
                    wide{
                        "Мескерем",
                        "Текемт",
                        "Хедар",
                        "Тахсас",
                        "Тер",
                        "Јекатит",
                        "Мегабит",
                        "Миазиа",
                        "Генбот",
                        "Сене",
                        "Хамле",
                        "Нехасе",
                        "Пагумен",
                    }
                }
            }
        }
        generic{
            DateTimePatterns{
                "HH:mm:ss zzzz",
                "HH:mm:ss z",
                "HH:mm:ss",
                "HH:mm",
                "EEEE, dd. MMMM y. G",
                "dd. MMMM y. G",
                "dd.MM.y. G",
                "d.M.y. GGGGG",
                "{1} {0}",
                "{1} {0}",
                "{1} {0}",
                "{1} {0}",
                "{1} {0}",
            }
            availableFormats{
                Ed{"E, d."}
                Gy{"y. G"}
                GyMMM{"MMM y. G"}
                GyMMMEd{"E, dd. MMM y. G"}
                GyMMMd{"dd. MMM y. G"}
                Hm{"HH:mm"}
                Hms{"HH:mm:ss"}
                M{"L"}
                MEd{"E, dd.MM."}
                MMM{"LLL"}
                MMMEd{"E, dd. MMM"}
                MMMd{"dd. MMM"}
                Md{"dd.MM."}
                d{"d"}
                h{"hh a"}
                hm{"hh:mm a"}
                hms{"hh:mm:ss a"}
                ms{"mm:ss"}
                y{"y. G"}
                yyyy{"y. G"}
                yyyyM{"MM.y. G"}
                yyyyMEd{"E, dd.MM.y. G"}
                yyyyMMM{"MMM y. G"}
                yyyyMMMEd{"E, dd. MMM y. G"}
                yyyyMMMd{"dd. MMM y. G"}
                yyyyMd{"dd.MM.y. G"}
                yyyyQQQ{"G y QQQ"}
                yyyyQQQQ{"G y QQQQ"}
            }
            intervalFormats{
                H{
                    H{"HH-HH"}
                }
                Hm{
                    H{"HH:mm-HH:mm"}
                    m{"HH:mm-HH:mm"}
                }
                Hmv{
                    H{"HH:mm-HH:mm v"}
                    m{"HH:mm-HH:mm v"}
                }
                Hv{
                    H{"HH-HH v"}
                }
                M{
                    M{"M-M"}
                }
                MEd{
                    M{"E, d.M - E, d.M"}
                    d{"E, d.M - E, d.M"}
                }
                MMM{
                    M{"MMM-MMM"}
                }
                MMMEd{
                    M{"E, dd. MMM - E, dd. MMM"}
                    d{"E, dd. - E, dd. MMM"}
                }
                MMMd{
                    M{"dd. MMM - dd. MMM"}
                    d{"dd.-dd. MMM"}
                }
                Md{
                    M{"d.M - d.M"}
                    d{"d.M - d.M"}
                }
                d{
                    d{"d-d"}
                }
                fallback{"{0} - {1}"}
                h{
                    a{"hh a - hh a"}
                    h{"hh-hh a"}
                }
                hm{
                    a{"hh:mm a - hh:mm a"}
                    h{"hh:mm-hh:mm a"}
                    m{"hh:mm-hh:mm a"}
                }
                hmv{
                    a{"hh:mm a - hh:mm a v"}
                    h{"hh:mm-hh:mm a v"}
                    m{"hh:mm-hh:mm a v"}
                }
                hv{
                    a{"hh a - hh a v"}
                    h{"hh-hh a v"}
                }
                y{
                    y{"y-y G"}
                }
                yM{
                    M{"MM.-MM. y. G"}
                    y{"MM.y. - MM.y. G"}
                }
                yMEd{
                    M{"E, d.M.y. - E, d.M.y. G"}
                    d{"E, d.M.y. - E, d.M.y. G"}
                    y{"E, d.M.y. - E, d.M.y. G"}
                }
                yMMM{
                    M{"MMM-MMM y. G"}
                    y{"MMM y. - MMM y. G"}
                }
                yMMMEd{
                    M{"E, dd. MMM - E, dd. MMM y. G"}
                    d{"E, dd. - E, dd. MMM y. G"}
                    y{"E, dd. MMM y. - E, dd. MMM y. G"}
                }
                yMMMM{
                    M{"MMMM-MMMM y. G"}
                    y{"MMMM y. - MMMM y. G"}
                }
                yMMMd{
                    M{"dd. MMM - dd. MMM y. G"}
                    d{"dd.-dd. MMM y. G"}
                    y{"dd. MMM y. - dd. MMM y. G"}
                }
                yMd{
                    M{"d.M.y. - d.M.y. G"}
                    d{"d.M.y. - d.M.y. G"}
                    y{"d.M.y. - d.M.y. G"}
                }
            }
        }
        gregorian{
            AmPmMarkers{
                "пре подне",
                "поподне",
            }
            DateTimePatterns{
                "HH:mm:ss zzzz",
                "HH:mm:ss z",
                "HH:mm:ss",
                "HH:mm",
                "EEEE, dd. MMMM y.",
                "dd. MMMM y.",
                "dd.MM.y.",
                "d.M.yy.",
                "{1} {0}",
                "{1} {0}",
                "{1} {0}",
                "{1} {0}",
                "{1} {0}",
            }
            availableFormats{
                Ed{"E, d."}
                Gy{"y. G"}
                GyMMM{"MMM y. G"}
                GyMMMEd{"E, dd. MMM y. G"}
                GyMMMd{"dd. MMM y. G"}
                Hm{"HH:mm"}
                Hms{"HH:mm:ss"}
                M{"L"}
                MEd{"E, dd.MM."}
                MMM{"LLL"}
                MMMEd{"E, dd. MMM"}
                MMMd{"dd. MMM"}
                Md{"dd.MM."}
                d{"d"}
                h{"hh a"}
                hm{"hh:mm a"}
                hms{"hh:mm:ss a"}
                ms{"mm:ss"}
                y{"y."}
                yM{"MM.y."}
                yMEd{"E, dd.MM.y."}
                yMMM{"MMM y."}
                yMMMEd{"E, dd. MMM y."}
                yMMMd{"dd. MMM y."}
                yMd{"dd.MM.y."}
                yQQQ{"y QQQ"}
                yQQQQ{"y QQQQ"}
            }
            dayNames{
                format{
                    abbreviated{
                        "нед",
                        "пон",
                        "уто",
                        "сри",
                        "чет",
                        "пет",
                        "суб",
                    }
                    wide{
                        "недеља",
                        "понедељак",
                        "уторак",
                        "сриједа",
                        "четвртак",
                        "петак",
                        "субота",
                    }
                }
                stand-alone{
                    narrow{
                        "н",
                        "п",
                        "у",
                        "с",
                        "ч",
                        "п",
                        "с",
                    }
                }
            }
            eras{
                abbreviated{
                    "п. н. е.",
                    "н. е.",
                }
                narrow{
                    "п.н.е.",
                    "н.е.",
                }
                wide{
                    "Пре нове ере",
                    "Нове ере",
                }
            }
            intervalFormats{
                H{
                    H{"HH-HH"}
                }
                Hm{
                    H{"HH:mm-HH:mm"}
                    m{"HH:mm-HH:mm"}
                }
                Hmv{
                    H{"HH:mm-HH:mm v"}
                    m{"HH:mm-HH:mm v"}
                }
                Hv{
                    H{"HH-HH v"}
                }
                M{
                    M{"M-M"}
                }
                MEd{
                    M{"E, d.M - E, d.M"}
                    d{"E, d.M - E, d.M"}
                }
                MMM{
                    M{"MMM-MMM"}
                }
                MMMEd{
                    M{"E, dd. MMM - E, dd. MMM"}
                    d{"E, dd. - E, dd. MMM"}
                }
                MMMd{
                    M{"dd. MMM - dd. MMM"}
                    d{"dd.-dd. MMM"}
                }
                Md{
                    M{"d.M - d.M"}
                    d{"d.M - d.M"}
                }
                d{
                    d{"d-d"}
                }
                fallback{"{0} - {1}"}
                h{
                    a{"hh a - hh a"}
                    h{"hh-hh a"}
                }
                hm{
                    a{"hh:mm a - hh:mm a"}
                    h{"hh:mm-hh:mm a"}
                    m{"hh:mm-hh:mm a"}
                }
                hmv{
                    a{"hh:mm a - hh:mm a v"}
                    h{"hh:mm-hh:mm a v"}
                    m{"hh:mm-hh:mm a v"}
                }
                hv{
                    a{"hh a - hh a v"}
                    h{"hh-hh a v"}
                }
                y{
                    y{"y-y"}
                }
                yM{
                    M{"MM.-MM. y."}
                    y{"MM.y. - MM.y."}
                }
                yMEd{
                    M{"E, d.M.y. - E, d.M.y."}
                    d{"E, d.M.y. - E, d.M.y."}
                    y{"E, d.M.y. - E, d.M.y."}
                }
                yMMM{
                    M{"MMM-MMM y."}
                    y{"MMM y. - MMM y."}
                }
                yMMMEd{
                    M{"E, dd. MMM - E, dd. MMM y."}
                    d{"E, dd. - E, dd. MMM y."}
                    y{"E, dd. MMM y. - E, dd. MMM y."}
                }
                yMMMM{
                    M{"MMMM-MMMM y."}
                    y{"MMMM y. - MMMM y."}
                }
                yMMMd{
                    M{"dd. MMM - dd. MMM y."}
                    d{"dd.-dd. MMM y."}
                    y{"dd. MMM y. - dd. MMM y."}
                }
                yMd{
                    M{"d.M.y. - d.M.y."}
                    d{"d.M.y. - d.M.y."}
                    y{"d.M.y. - d.M.y."}
                }
            }
            monthNames{
                format{
                    abbreviated{
                        "јан",
                        "феб",
                        "мар",
                        "апр",
                        "мај",
                        "јун",
                        "јул",
                        "авг",
                        "сеп",
                        "окт",
                        "нов",
                        "дец",
                    }
                    wide{
                        "јануар",
                        "фебруар",
                        "март",
                        "април",
                        "мај",
                        "јуни",
                        "јули",
                        "август",
                        "септембар",
                        "октобар",
                        "новембар",
                        "децембар",
                    }
                }
                stand-alone{
                    narrow{
                        "ј",
                        "ф",
                        "м",
                        "а",
                        "м",
                        "ј",
                        "ј",
                        "а",
                        "с",
                        "о",
                        "н",
                        "д",
                    }
                }
            }
            quarters{
                format{
                    abbreviated{
                        "К1",
                        "К2",
                        "К3",
                        "К4",
                    }
                    narrow{
                        "1.",
                        "2.",
                        "3.",
                        "4.",
                    }
                    wide{
                        "Прво тромесечје",
                        "Друго тромесечје",
                        "Треће тромесечје",
                        "Четврто тромесечје",
                    }
                }
                stand-alone{
                    narrow{
                        "1.",
                        "2.",
                        "3.",
                        "4.",
                    }
                }
            }
        }
        hebrew{
            monthNames{
                format{
                    wide{
                        "Тишри",
                        "Хешван",
                        "Кислев",
                        "Тевет",
                        "Шеват",
                        "Адар I",
                        "Адар",
                        "Нисан",
                        "Ијар",
                        "Сиван",
                        "Тамуз",
                        "Ав",
                        "Елул",
                        "Адар II",
                    }
                }
            }
        }
        indian{
            eras{
                abbreviated{
                    "САКА",
                }
            }
            monthNames{
                format{
                    wide{
                        "Чаитра",
                        "Ваисака",
                        "Јиаиста",
                        "Асада",
                        "Сравана",
                        "Бадра",
                        "Асвина",
                        "Картика",
                        "Аргајана",
                        "Пауза",
                        "Мага",
                        "Фалгуна",
                    }
                }
            }
        }
        islamic{
            DateTimePatterns{
                "HH:mm:ss zzzz",
                "HH:mm:ss z",
                "HH:mm:ss",
                "HH:mm",
                "EEEE, dd. MMMM y. G",
                "dd. MMMM y. G",
                "dd.MM.y. G",
                "dd.MM.y. G",
                "{1} {0}",
                "{1} {0}",
                "{1} {0}",
                "{1} {0}",
                "{1} {0}",
            }
            availableFormats{
                Ed{"E, dd."}
                Hm{"HH:mm"}
                Hms{"HH:mm:ss"}
                M{"L"}
                MEd{"E, dd.MM."}
                MMM{"LLL"}
                MMMEd{"E, dd. MMM"}
                MMMd{"dd. MMM"}
                Md{"dd.MM."}
                d{"d"}
                hm{"hh:mm a"}
                hms{"hh:mm:ss a"}
                ms{"mm:ss"}
                y{"y. G"}
                yM{"MM.y. G"}
                yMEd{"E, dd.MM.y. G"}
                yMMM{"MMM y. G"}
                yMMMEd{"E, dd. MMM y. G"}
                yMMMd{"dd. MMM y. G"}
                yMd{"dd.MM.y. G"}
                yQQQ{"y G QQQ"}
                yQQQQ{"y G QQQQ"}
            }
            eras{
                abbreviated{
                    "АХ",
                }
            }
            monthNames{
                format{
                    wide{
                        "Мурахам",
                        "Сафар",
                        "Рабиʻ I",
                        "Рабиʻ II",
                        "Јумада I",
                        "Јумада II",
                        "Рађаб",
                        "Шаʻбан",
                        "Рамадан",
                        "Шавал",
                        "Дуʻл-Киʻда",
                        "Дуʻл-хиђа",
                    }
                }
            }
        }
        japanese{
            DateTimePatterns{
                "HH:mm:ss zzzz",
                "HH:mm:ss z",
                "HH:mm:ss",
                "HH:mm",
                "EEEE, MMMM d, y G",
                "MMMM d, y G",
                "MMM d, y G",
                "M/d/yy G",
                "{1} {0}",
                "{1} {0}",
                "{1} {0}",
                "{1} {0}",
                "{1} {0}",
            }
            eras{
                abbreviated{
                    "Таика (645-650)",
                    "Хакучи (650-671)",
                    "Хакухо (672-686)",
                    "Шучо (686-701)",
                    "Таихо (701-704)",
                    "Кеиун (704-708)",
                    "Вадо (708-715)",
                    "Реики (715-717)",
                    "Јоро (717-724)",
                    "Јинки (724-729)",
                    "Темпио (729-749)",
                    "Темпио-кампо (749-749)",
                    "Темпио-шохо (749-757)",
                    "Темпио-хођи (757-765)",
                    "Темпо-ђинго (765-767)",
                    "Ђинго-кеиун (767-770)",
                    "Хоки (770-780)",
                    "Тен-о (781-782)",
                    "Енрјаку (782-806)",
                    "Даидо (806-810)",
                    "Конин (810-824)",
                    "Тенчо (824-834)",
                    "Шова (834-848)",
                    "Кајо (848-851)",
                    "Нињу (851-854)",
                    "Саико (854-857)",
                    "Тенан (857-859)",
                    "Јоган (859-877)",
                    "Генкеи (877-885)",
                    "Ниња (885-889)",
                    "Кампјо (889-898)",
                    "Шотаи (898-901)",
                    "Енђи (901-923)",
                    "Енчо (923-931)",
                    "Шохеи (931-938)",
                    "Тенгјо (938-947)",
                    "Тенриаку (947-957)",
                    "Тентоку (957-961)",
                    "Ова (961-964)",
                    "Кохо (964-968)",
                    "Ана (968-970)",
                    "Тенроку (970-973)",
                    "Тен-ен (973-976)",
                    "Јоген (976-978)",
                    "Тенген (978-983)",
                    "Еикан (983-985)",
                    "Кана (985-987)",
                    "Еи-ен (987-989)",
                    "Еисо (989-990)",
                    "Шорјаку (990-995)",
                    "Чотоку (995-999)",
                    "Чохо (999-1004)",
                    "Канко (1004-1012)",
                    "Чова (1012-1017)",
                    "Канин (1017-1021)",
                    "Ђиан (1021-1024)",
                    "Мању (1024-1028)",
                    "Чоген (1028-1037)",
                    "Чорјаку (1037-1040)",
                    "Чокју (1040-1044)",
                    "Кантоку (1044-1046)",
                    "Еишо (1046-1053)",
                    "Тенђи (1053-1058)",
                    "Кохеи (1058-1065)",
                    "Ђирјаку (1065-1069)",
                    "Енкју (1069-1074)",
                    "Шохо (1074-1077)",
                    "Шорјаку (1077-1081)",
                    "Еишо (1081-1084)",
                    "Отоку (1084-1087)",
                    "Канђи (1087-1094)",
                    "Кахо (1094-1096)",
                    "Еичо (1096-1097)",
                    "Шотоку (1097-1099)",
                    "Кова (1099-1104)",
                    "Чођи (1104-1106)",
                    "Кашо (1106-1108)",
                    "Тенин (1108-1110)",
                    "Тен-еи (1110-1113)",
                    "Еикју (1113-1118)",
                    "Ђен-еи (1118-1120)",
                    "Хоан (1120-1124)",
                    "Тенђи (1124-1126)",
                    "Даиђи (1126-1131)",
                    "Теншо (1131-1132)",
                    "Чошао (1132-1135)",
                    "Хоен (1135-1141)",
                    "Еиђи (1141-1142)",
                    "Кођи (1142-1144)",
                    "Тењо (1144-1145)",
                    "Кјуан (1145-1151)",
                    "Нинпеи (1151-1154)",
                    "Кјују (1154-1156)",
                    "Хоген (1156-1159)",
                    "Хеиђи (1159-1160)",
                    "Еирјаку (1160-1161)",
                    "Охо (1161-1163)",
                    "Чокан (1163-1165)",
                    "Еиман (1165-1166)",
                    "Нин-ан (1166-1169)",
                    "Као (1169-1171)",
                    "Шоан (1171-1175)",
                    "Анген (1175-1177)",
                    "Ђишо (1177-1181)",
                    "Јова (1181-1182)",
                    "Ђуеи (1182-1184)",
                    "Генрјуку (1184-1185)",
                    "Бунђи (1185-1190)",
                    "Кенкју (1190-1199)",
                    "Шођи (1199-1201)",
                    "Кенин (1201-1204)",
                    "Генкју (1204-1206)",
                    "Кен-еи (1206-1207)",
                    "Шоген (1207-1211)",
                    "Кенрјаку (1211-1213)",
                    "Кенпо (1213-1219)",
                    "Шокју (1219-1222)",
                    "Ђу (1222-1224)",
                    "Ђенин (1224-1225)",
                    "Кароку (1225-1227)",
                    "Антеи (1227-1229)",
                    "Канки (1229-1232)",
                    "Ђоеи (1232-1233)",
                    "Темпуку (1233-1234)",
                    "Бунрјаку (1234-1235)",
                    "Катеи (1235-1238)",
                    "Рјакунин (1238-1239)",
                    "Ен-о (1239-1240)",
                    "Нињи (1240-1243)",
                    "Канген (1243-1247)",
                    "Хођи (1247-1249)",
                    "Кенчо (1249-1256)",
                    "Коген (1256-1257)",
                    "Шока (1257-1259)",
                    "Шоген (1259-1260)",
                    "Бун-о (1260-1261)",
                    "Кочо (1261-1264)",
                    "Бун-еи (1264-1275)",
                    "Кенђи (1275-1278)",
                    "Коан (1278-1288)",
                    "Шу (1288-1293)",
                    "Еинин (1293-1299)",
                    "Шоан (1299-1302)",
                    "Кенген (1302-1303)",
                    "Каген (1303-1306)",
                    "Токуђи (1306-1308)",
                    "Енкеи (1308-1311)",
                    "Очо (1311-1312)",
                    "Шова (1312-1317)",
                    "Бунпо (1317-1319)",
                    "Ђено (1319-1321)",
                    "Ђенкјо (1321-1324)",
                    "Шочу (1324-1326)",
                    "Кареки (1326-1329)",
                    "Гентоку (1329-1331)",
                    "Генко (1331-1334)",
                    "Кему (1334-1336)",
                    "Енген (1336-1340)",
                    "Кококу (1340-1346)",
                    "Шохеи (1346-1370)",
                    "Кентоку (1370-1372)",
                    "Бучу (1372-1375)",
                    "Тењу (1375-1379)",
                    "Корјаку (1379-1381)",
                    "Кова (1381-1384)",
                    "Генчу (1384-1392)",
                    "Меитоку (1384-1387)",
                    "Какеи (1387-1389)",
                    "Ку (1389-1390)",
                    "Меитоку (1390-1394)",
                    "Оеи (1394-1428)",
                    "Шочо (1428-1429)",
                    "Еикјо (1429-1441)",
                    "Какитсу (1441-1444)",
                    "Бун-ан (1444-1449)",
                    "Хотоку (1449-1452)",
                    "Кјотоку (1452-1455)",
                    "Кошо (1455-1457)",
                    "Чороку (1457-1460)",
                    "Каншо (1460-1466)",
                    "Буншо (1466-1467)",
                    "Онин (1467-1469)",
                    "Бунмеи (1469-1487)",
                    "Чокјо (1487-1489)",
                    "Ентоку (1489-1492)",
                    "Меио (1492-1501)",
                    "Бунки (1501-1504)",
                    "Еишо (1504-1521)",
                    "Таиеи (1521-1528)",
                    "Кјороку (1528-1532)",
                    "Тенмон (1532-1555)",
                    "Кођи (1555-1558)",
                    "Еироку (1558-1570)",
                    "Генки (1570-1573)",
                    "Теншо (1573-1592)",
                    "Бунроку (1592-1596)",
                    "Кеичо (1596-1615)",
                    "Генва (1615-1624)",
                    "Кан-еи (1624-1644)",
                    "Шохо (1644-1648)",
                    "Кеиан (1648-1652)",
                    "Шу (1652-1655)",
                    "Меирјаку (1655-1658)",
                    "Мањи (1658-1661)",
                    "Канбун (1661-1673)",
                    "Енпо (1673-1681)",
                    "Тенва (1681-1684)",
                    "Јокјо (1684-1688)",
                    "Генроку (1688-1704)",
                    "Хоеи (1704-1711)",
                    "Шотоку (1711-1716)",
                    "Кјохо (1716-1736)",
                    "Генбун (1736-1741)",
                    "Канпо (1741-1744)",
                    "Енкјо (1744-1748)",
                    "Кан-ен (1748-1751)",
                    "Хорјаку (1751-1764)",
                    "Меива (1764-1772)",
                    "Ан-еи (1772-1781)",
                    "Тенмеи (1781-1789)",
                    "Кансеи (1789-1801)",
                    "Кјова (1801-1804)",
                    "Бунка (1804-1818)",
                    "Бунсеи (1818-1830)",
                    "Тенпо (1830-1844)",
                    "Кока (1844-1848)",
                    "Каеи (1848-1854)",
                    "Ансеи (1854-1860)",
                    "Ман-ен (1860-1861)",
                    "Бункју (1861-1864)",
                    "Генђи (1864-1865)",
                    "Кеико (1865-1868)",
                    "Меиђи",
                    "Таишо",
                    "Шова",
                    "Хаисеи",
                }
            }
        }
        persian{
            monthNames{
                format{
                    wide{
                        "Фаравадин",
                        "Ордибехешт",
                        "Кордад",
                        "Тир",
                        "Мордад",
                        "Шахривар",
                        "Мехр",
                        "Абан",
                        "Азар",
                        "Деј",
                        "Бахман",
                        "Есфанд",
                    }
                }
            }
        }
        roc{
            eras{
                abbreviated{
                    "Пре РК",
                    "РК",
                }
            }
        }
    }
    delimiters{
        alternateQuotationEnd{"‘"}
        alternateQuotationStart{"‚"}
        quotationEnd{"“"}
        quotationStart{"„"}
    }
    fields{
        day{
            dn{"дан"}
            relative{
                "-1"{"јуче"}
                "-2"{"прекјуче"}
                "0"{"данас"}
                "1"{"сутра"}
                "2"{"прекосутра"}
            }
            relativeTime{
                future{
                    few{"за {0} дана"}
                    many{"за {0} дана"}
                    one{"за {0} дан"}
                    other{"за {0} дана"}
                }
                past{
                    few{"пре {0} дана"}
                    many{"пре {0} дана"}
                    one{"пре {0} дан"}
                    other{"пре {0} дана"}
                }
            }
        }
        dayperiod{
            dn{"пре подне/поподне"}
        }
        era{
            dn{"ера"}
        }
        hour{
            dn{"час"}
            relativeTime{
                future{
                    few{"за {0} сата"}
                    many{"за {0} сати"}
                    one{"за {0} сат"}
                    other{"за {0} сати"}
                }
                past{
                    few{"пре {0} сата"}
                    many{"пре {0} сати"}
                    one{"пре {0} сат"}
                    other{"пре {0} сати"}
                }
            }
        }
        minute{
            dn{"минут"}
            relativeTime{
                future{
                    few{"за {0} минута"}
                    many{"за {0} минута"}
                    one{"за {0} минут"}
                    other{"за {0} минута"}
                }
                past{
                    few{"пре {0} минута"}
                    many{"пре {0} минута"}
                    one{"пре {0} минут"}
                    other{"пре {0} минута"}
                }
            }
        }
        month{
            dn{"месец"}
            relative{
                "-1"{"Прошлог месеца"}
                "0"{"Овог месеца"}
                "1"{"Следећег месеца"}
            }
            relativeTime{
                future{
                    few{"за {0} месеца"}
                    many{"за {0} месеци"}
                    one{"за {0} месец"}
                    other{"за {0} месеци"}
                }
                past{
                    few{"пре {0} месеца"}
                    many{"пре {0} месеци"}
                    one{"пре {0} месец"}
                    other{"пре {0} месеци"}
                }
            }
        }
        second{
            dn{"секунд"}
            relativeTime{
                future{
                    few{"за {0} секунде"}
                    many{"за {0} секунди"}
                    one{"за {0} секунд"}
                    other{"за {0} секунди"}
                }
                past{
                    few{"пре {0} секунде"}
                    many{"пре {0} секунди"}
                    one{"пре {0} секунд"}
                    other{"пре {0} секунди"}
                }
            }
        }
        week{
            dn{"недеља"}
            relative{
                "-1"{"Прошле недеље"}
                "0"{"Ове недеље"}
                "1"{"Следеће недеље"}
            }
            relativeTime{
                future{
                    few{"за {0} недеље"}
                    many{"за {0} недеља"}
                    one{"за {0} недељу"}
                    other{"за {0} недеља"}
                }
                past{
                    few{"пре {0} недеље"}
                    many{"пре {0} недеља"}
                    one{"пре {0} недељу"}
                    other{"пре {0} недеља"}
                }
            }
        }
        weekday{
            dn{"дан у недељи"}
        }
        year{
            dn{"година"}
            relative{
                "-1"{"Прошле године"}
                "0"{"Ове године"}
                "1"{"Следеће године"}
            }
            relativeTime{
                future{
                    few{"за {0} године"}
                    many{"за {0} година"}
                    one{"за {0} годину"}
                    other{"за {0} година"}
                }
                past{
                    few{"пре {0} године"}
                    many{"пре {0} година"}
                    one{"пре {0} годину"}
                    other{"пре {0} година"}
                }
            }
        }
        zone{
            dn{"зона"}
        }
    }
    listPattern{
        standard{
            2{"{0} и {1}"}
            end{"{0} и {1}"}
        }
    }
    measurementSystemNames{
        UK{"империјални"}
        US{"САД"}
        metric{"Метрички"}
    }
    transformNames{
        BGN{"БГН (BGN)"}
        Numeric{"Нумеричка"}
        Tone{"Тон"}
        UNGEGN{"УНГЕГН (BGN)"}
        x-Accents{"Акценти"}
        x-Fullwidth{"пуна ширина"}
        x-Halfwidth{"пола ширине"}
        x-Jamo{"Џамо"}
        x-Pinyin{"Пинјин"}
        x-Publishing{"Издавачки"}
    }
    units{
        duration{
            day{
                few{"{0} дана"}
                many{"{0} дана"}
                one{"{0} дан"}
                other{"{0} дан"}
            }
            hour{
                few{"{0} сата"}
                many{"{0} сати"}
                one{"{0} сат"}
                other{"{0} сат"}
            }
            minute{
                few{"{0} минута"}
                many{"{0} минута"}
                one{"{0} минут"}
                other{"{0} минут"}
            }
            month{
                few{"{0} месеца"}
                many{"{0} месеци"}
                one{"{0} месец"}
                other{"{0} месец"}
            }
            second{
                few{"{0} секунде"}
                many{"{0} секунди"}
                one{"{0} секунда"}
                other{"{0} секунда"}
            }
            week{
                few{"{0} недеље"}
                many{"{0} недеља"}
                one{"{0} недеља"}
                other{"{0} недеља"}
            }
            year{
                few{"{0} године"}
                many{"{0} година"}
                one{"{0} година"}
                other{"{0} година"}
            }
        }
    }
    unitsShort{
        duration{
            day{
                few{"{0} дана"}
                many{"{0} дана"}
                one{"{0} дан"}
                other{"{0} дан"}
            }
            hour{
                few{"{0} сата"}
                many{"{0} сати"}
                one{"{0} сат"}
                other{"{0} сат"}
            }
            minute{
                few{"{0} мин"}
                many{"{0} мин"}
                one{"{0} мин"}
                other{"{0} мин"}
            }
            month{
                few{"{0} мес"}
                many{"{0} мес"}
                one{"{0} мес"}
                other{"{0} мес"}
            }
            second{
                few{"{0} сек"}
                many{"{0} сек"}
                one{"{0} сек"}
                other{"{0} сек"}
            }
            week{
                few{"{0} нед"}
                many{"{0} нед"}
                one{"{0} нед"}
                other{"{0} нед"}
            }
            year{
                few{"{0} год"}
                many{"{0} год"}
                one{"{0} год"}
                other{"{0} год"}
            }
        }
    }
}

// ***************************************************************************
// *
// * Copyright (C) 2014 International Business Machines
// * Corporation and others. All Rights Reserved.
// * Tool: org.unicode.cldr.icu.NewLdml2IcuConverter
// * Source File: <path>/common/rbnf/th.xml
// *
// ***************************************************************************
/**
 * ICU <specials> source: <path>/xml/rbnf/th.xml
 */
th{
    RBNFRules{
        OrdinalRules{
            "%digits-ordinal:",
            "-x: \u0E17\u0E35\u0E48\u2212>#,##0>;",
            "0: \u0E17\u0E35\u0E48\u200B=#,##0=;",
        }
        SpelloutRules{
            "%spellout-numbering-year:",
            "x.x: =#,###0.#=;",
            "0: =%spellout-numbering=;",
            "%spellout-numbering:",
            "0: =%spellout-cardinal=;",
            "%spellout-cardinal:",
            "-x: \u0E25\u0E1A\u200B>>;",
            "x.x: <<\u200B\u0E08\u0E38\u0E14\u200B>>>;",
            "0: \u0E28\u0E39\u0E19\u0E22\u0E4C;",
            "1: \u0E2B\u0E19\u0E36\u0E48\u0E07;",
            "2: \u0E2A\u0E2D\u0E07;",
            "3: \u0E2A\u0E32\u0E21;",
            "4: \u0E2A\u0E35\u0E48;",
            "5: \u0E2B\u0E49\u0E32;",
            "6: \u0E2B\u0E01;",
            "7: \u0E40\u0E08\u0E47\u0E14;",
            "8: \u0E41\u0E1B\u0E14;",
            "9: \u0E40\u0E01\u0E49\u0E32;",
            "10: \u0E2A\u0E34\u0E1A[\u200B>%%alt-ones>];",
            "20: \u0E22\u0E35\u0E48\u200B\u0E2A\u0E34\u0E1A[\u200B>%%alt-ones>];",
            "30: <<\u200B\u0E2A\u0E34\u0E1A[\u200B>%%alt-ones>];",
            "100: <<\u200B\u0E23\u0E49\u0E2D\u0E22[\u200B>>];",
            "1000: <<\u200B\u0E1E\u0E31\u0E19[\u200B>>];",
            "10000: <<\u200B\u0E2B\u0E21\u0E37\u0E48\u0E19[\u200B>>];",
            "100000: <<\u200B\u0E41\u0E2A\u0E19[\u200B>>];",
            "1000000: <<\u200B\u0E25\u0E49\u0E32\u0E19[\u200B>>];",
            "1000000000000000000: =#,##0=;",
            "%%alt-ones:",
            "1: \u0E40\u0E2D\u0E47\u0E14;",
            "2: =%spellout-cardinal=;",
            "%spellout-ordinal:",
            "x.x: =#,##0.#=;",
            "0: \u0E17\u0E35\u0E48\u200B=%spellout-cardinal=;",
        }
    }
    Version{"2.0.82.42"}
}

<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>ICU 51.2</Name>
  <Location>/Engine/Source/ThirdParty/ICU/</Location>
  <Date>2016-06-10T12:02:52.5030733-04:00</Date>
  <Function>ICU is a mature, widely used set of C/C++ and Java libraries providing Unicode and Globalization support for software applications</Function>
  <Justification>Needed for UE4 ship</Justification>
  <Eula>http://www.unicode.org/copyright.html#License</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>/Engine/Source/ThirdParty/Licenses/ICU51.2_License.txt</LicenseFolder>
</TpsData>
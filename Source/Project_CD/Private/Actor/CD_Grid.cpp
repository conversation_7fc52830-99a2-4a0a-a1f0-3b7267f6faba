// Copyright Horizon Seeker Studios


#include "Actor/CD_Grid.h"

#include "AbilitySystemBlueprintLibrary.h"
#include "AbilitySystemComponent.h"
#include "GameplayEffect.h"
#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "Components/InstancedStaticMeshComponent.h"
#include "DataAssets/Rules/DataAsset_GridRules.h"
#include "Engine/AssetManager.h"
#include "Engine/StreamableManager.h"
#include "FunctionLibraries/CyberDuelsFunctionLibrary.h"
#include "FunctionLibraries/DS_FunctionLibrary.h"
#include "Net/UnrealNetwork.h"


ACD_Grid::ACD_Grid() : GridTilesArray(this)
{
	PrimaryActorTick.bCanEverTick = false;
	PrimaryActorTick.bStartWithTickEnabled = false;
	SetReplicatingMovement(false);
	bReplicates = true;

	ISMC_TeamBlue = CreateDefaultSubobject<UInstancedStaticMeshComponent>(TEXT("ISMC_TeamBlue"));
	ISMC_TeamBlue->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
	ISMC_TeamBlue->SetCollisionObjectType(ECC_WorldStatic);
	ISMC_TeamBlue->SetCollisionResponseToChannel(ECC_Pawn, ECR_Block);
	ISMC_TeamBlue->SetCollisionResponseToChannel(ECC_Camera, ECR_Ignore);
	ISMC_TeamBlue->NumCustomDataFloats = 100;
	RootComponent = ISMC_TeamBlue;

	ISMC_TeamRed = CreateDefaultSubobject<UInstancedStaticMeshComponent>(TEXT("ISMC_TeamRed"));
	ISMC_TeamRed->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
	ISMC_TeamRed->SetCollisionObjectType(ECC_WorldStatic);
	ISMC_TeamRed->SetCollisionResponseToChannel(ECC_Pawn, ECR_Block);
	ISMC_TeamRed->SetCollisionResponseToChannel(ECC_Camera, ECR_Ignore);
	ISMC_TeamRed->NumCustomDataFloats = 100;
}

void ACD_Grid::BeginPlay()
{
	Super::BeginPlay();

	CacheGridRules();

	if (!HasAuthority())
	{
		GridTilesArray.OnTileAddedDelegate.AddUObject(this, &ThisClass::Client_HandleTileAdded);
		GridTilesArray.OnTileChangedDelegate.AddUObject(this, &ThisClass::Client_HandleTileChanged);
		GridTilesArray.OnTileRemovedDelegate.AddUObject(this, &ThisClass::Client_HandleTileRemoved);
	}
	
	if (HasAuthority())
	{
		Server_InitializeGridData();
		const float ResetTileStateTimerRate = GridRules->GetGridConfigurationByTag(CurrentGridConfigurationTag).ResetTileStateTimerRate;
		GetWorldTimerManager().SetTimer(Server_ActiveStatesCheckTimerHandle, this,
			&ThisClass::Server_CheckActiveTileStates, ResetTileStateTimerRate, true);
	}
}

void ACD_Grid::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
	if (HasAuthority())
	{
		GetWorldTimerManager().ClearTimer(Server_ActiveStatesCheckTimerHandle);
	}

	// Clean up client-side Niagara components
	for (auto const& [Coords, StateMap] : ActiveTileNiagaraComponents)
	{
		for (auto const& [Tag, NiagaraCompPtr] : StateMap)
		{
			if (NiagaraCompPtr.IsValid())
			{
				NiagaraCompPtr->DestroyComponent();
			}
		}
	}
	ActiveTileNiagaraComponents.Empty();
	
	Super::EndPlay(EndPlayReason);
}

void ACD_Grid::OnConstruction(const FTransform& Transform)
{
	Super::OnConstruction(Transform);
	Editor_SpawnGridVisuals();
}

void ACD_Grid::CacheGridRules()
{
	if (!IsValid(GridRules))
	{
		GridRules = UCyberDuelsFunctionLibrary::Native_GetGridRules(this);
		check(GridRules); // This is a critical dependency, game can't run without it.
		
		if (UDataTable* TileStatesTable = GridRules->GetTileStatesTable())
		{
			UDS_FunctionLibrary::Native_ConvertDataTableToMap(TileStatesTable, TileStatesMap, &FCD_TileState::StateTag);
		}
		if (UDataTable* GridConfigurationsTable = GridRules->GetGridConfigurationsTable())
		{
			UDS_FunctionLibrary::Native_ConvertDataTableToMap(GridConfigurationsTable, GridConfigurationsMap, &FCD_GridConfiguration::GridConfigurationTag);
		}
		UE_LOG(LogTemp, Log, TEXT("%hs success for %s"), __FUNCTION__, *GetNameSafe(this));
	}
}

void ACD_Grid::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);

	DOREPLIFETIME(ACD_Grid, GridTilesArray);

	FDoRepLifetimeParams SharedParams;
	SharedParams.bIsPushBased = true;
	SharedParams.Condition = COND_None;
	SharedParams.RepNotifyCondition = REPNOTIFY_Always;

	DOREPLIFETIME_WITH_PARAMS_FAST(ThisClass, CurrentGridConfigurationTag, SharedParams);
}

bool ACD_Grid::IsTileOccupied(const FIntPoint& InGridPosition)
{
	if (const FCD_GridTileEntry* TileEntry = GetTileAtCoordinates(InGridPosition))
	{
		return TileEntry->IsTileOccupied();
	}
	return false;
}

FCD_GridTileEntry* ACD_Grid::GetTileAtCoordinates(const FIntPoint& InGridCoordinates)
{
	return GridTilesArray.GetTileByGridCoordinates(InGridCoordinates);
}

TArray<FCD_GridTileEntry> ACD_Grid::GetTilesByState(const FGameplayTag& InStateTag)
{
	return GridTilesArray.GetTilesByTag(InStateTag);
}

void ACD_Grid::UpdateOccupyingActorsAtTile(const FIntPoint& InGridCoordinates, const TWeakObjectPtr<AActor>& InNewActor)
{
	if (!HasAuthority()) return;
	GridTilesArray.UpdateOccupyingActorsAtTile(InGridCoordinates, InNewActor);
}

void ACD_Grid::ApplyStateToTiles(const FGameplayTag& InStateToApply, const TArray<FIntPoint>& InTargetCoordinates, AActor* InInstigator)
{
	if (!HasAuthority()) 
	{
		Server_ApplyStateToTiles(InStateToApply, InTargetCoordinates, InInstigator);
		return;
	}
	
	GridTilesArray.UpdateTilesState(InStateToApply, InTargetCoordinates, InInstigator);
}

void ACD_Grid::SetGridConfiguration(const FGameplayTag& InGridConfigurationTag)
{
	if (!HasAuthority())
	{
		Server_SetGridConfiguration(InGridConfigurationTag);
		return;
	}
	
	CurrentGridConfigurationTag = InGridConfigurationTag;
	MARK_PROPERTY_DIRTY_FROM_NAME(ThisClass, CurrentGridConfigurationTag, this);
}

void ACD_Grid::Editor_SpawnGridVisuals()
{
    UStaticMesh* BlueTeamStaticMesh = ISMC_TeamBlue->GetStaticMesh();
    UStaticMesh* RedTeamStaticMesh = ISMC_TeamRed->GetStaticMesh();
    
    if (!IsValid(BlueTeamStaticMesh) || !IsValid(RedTeamStaticMesh) || !IsValid(Editor_GridRules) ||
        !IsValid(TileMaterial_TeamBlue) || !IsValid(TileMaterial_TeamRed)) return;
    
    // Clear existing instances and components
    ISMC_TeamBlue->ClearInstances();
    ISMC_TeamBlue->SetStaticMesh(BlueTeamStaticMesh);
    ISMC_TeamBlue->SetMaterial(0, TileMaterial_TeamBlue);
    ISMC_TeamRed->ClearInstances();
    ISMC_TeamRed->SetStaticMesh(RedTeamStaticMesh);
    ISMC_TeamRed->SetMaterial(0, TileMaterial_TeamRed);

    // Clean up existing visual components
    for (const auto& NiagaraComponent : Editor_TileActiveNiagaraComponents)
    {
        if (NiagaraComponent.IsValid())
        {
            NiagaraComponent->DestroyComponent();
        }
    }

    for (const auto& Actor : Editor_TileOccupyingActors)
    {
        if (Actor.IsValid())
        {
            Actor->Destroy();
        }
    }
    
    Editor_TileOccupyingActors.Reset();
    Editor_TileActiveNiagaraComponents.Reset();
    
    FCD_GridConfiguration FoundConfig = Editor_GridRules->GetGridConfigurationByTag(CurrentGridConfigurationTag);
    
    FIntPoint GridSize = FoundConfig.GridSize;
    FVector TileSpacing = FoundConfig.TilesSpacing;
    FVector GridTileSize = FoundConfig.GridTilesSize;
    FRotator TileRotation = FoundConfig.TilesRotation;
	FGameplayTag DefaultTileStateTag = FoundConfig.DefaultTileStateTag;
	FGameplayTag NeutralTeamTag = FoundConfig.NeutralTeamTag;
	FGameplayTag BlueTeamTag = FoundConfig.BlueTeamTag;
	FGameplayTag RedTeamTag = FoundConfig.RedTeamTag;
    int32 GridSegments = FoundConfig.GridSegments;
    int32 RedTeamColumns = FoundConfig.RedTeamColumns;
    TMap<FIntPoint, FCD_TileConfiguration> TileConfigurations = FoundConfig.TilesConfig;
    
    for (int32 X = 0; X < GridSize.X; ++X)
    {
        for (int32 Y = 0; Y < GridSize.Y; ++Y)
        {
            const FIntPoint TileCoordinates(X, Y);
            const FVector TileLocation = GetActorLocation() + FVector(X * TileSpacing.X, Y * TileSpacing.Y, TileSpacing.Z);
            const FVector TileSize = GridTileSize;
            FTransform InstanceTransform(TileRotation.Quaternion(), TileLocation, TileSize);
            
            // Determine which ISMC this tile belongs to
            UInstancedStaticMeshComponent* TargetISMC = X < GridSize.X / GridSegments ? ISMC_TeamRed : ISMC_TeamBlue;
            int32 InstanceIndex = TargetISMC->AddInstance(InstanceTransform);
            
            if (TileConfigurations.Contains(TileCoordinates))
            {
                auto [TileTags, ActorsToSpawnList] = TileConfigurations.FindRef(TileCoordinates);
                
                for (const FGameplayTag& StateTag : TileTags)
                {
                    Editor_UpdateTileVisuals(InstanceIndex, TileLocation, TargetISMC, StateTag);
                }
            	Editor_SpawnActorsOnTile(TileLocation, ActorsToSpawnList);
            }
            else
            {
                Editor_UpdateTileVisuals(InstanceIndex, TileLocation, TargetISMC, DefaultTileStateTag);
                
                if (X < RedTeamColumns)
                {
                    Editor_UpdateTileVisuals(InstanceIndex, TileLocation, TargetISMC, RedTeamTag);
                }
                else if (X >= GridSize.X - RedTeamColumns)
                {
                    Editor_UpdateTileVisuals(InstanceIndex, TileLocation, TargetISMC, BlueTeamTag);
                }
                else
                {
                    Editor_UpdateTileVisuals(InstanceIndex, TileLocation, TargetISMC, NeutralTeamTag);
                }
            }
        }
    }
}

void ACD_Grid::Editor_UpdateTileVisuals(const int32 InTileIndex, const FVector& InTileLocation, UInstancedStaticMeshComponent* InISMC, const FGameplayTag& InStateTag)
{
	const FCD_TileState TileStateConfig = Editor_GridRules->GetTileStateByTag(InStateTag);
	
	if (TileStateConfig.bChangesMaterial)
	{
		for (const auto& Pair : TileStateConfig.CustomDataFloats)
		{
			InISMC->SetCustomDataValue(InTileIndex, Pair.Key, Pair.Value.TargetCustomDataValue, true);
		}
	}
	
	if (TileStateConfig.bHasVisualEffect)
	{
		if (TileStateConfig.TileStateVisualEffect.IsValid())
		{
			const FVector EffectLocation = InTileLocation + TileStateConfig.VisualEffectTransform.GetLocation();
			const FRotator EffectRotation = TileStateConfig.VisualEffectTransform.Rotator(); 
			const FVector EffectScale = TileStateConfig.VisualEffectTransform.GetScale3D();

			UNiagaraComponent* NiagaraComponent = UNiagaraFunctionLibrary::SpawnSystemAtLocation(
				this,
				TileStateConfig.TileStateVisualEffect.Get(),
				EffectLocation,
				EffectRotation,
				EffectScale,
				TileStateConfig.bAutoDestroyVisualEffect,
				TileStateConfig.bAutoActivateVisualEffect,
				TileStateConfig.VisualEffectPoolingMethod,
				TileStateConfig.bPreCullCheckVisualEffect
			);
			
			Editor_TileActiveNiagaraComponents.Add(NiagaraComponent);
		}
		else
		{
			FStreamableManager& StreamableManager = UAssetManager::GetStreamableManager();
			StreamableManager.RequestAsyncLoad(TileStateConfig.TileStateVisualEffect.ToSoftObjectPath(),
			FStreamableDelegate::CreateWeakLambda(this,[this, TileStateConfig, InTileLocation]()
			{
				const FVector EffectLocation = InTileLocation + TileStateConfig.VisualEffectTransform.GetLocation();
				const FRotator EffectRotation = TileStateConfig.VisualEffectTransform.Rotator(); 
				const FVector EffectScale = TileStateConfig.VisualEffectTransform.GetScale3D();

				UNiagaraComponent* NiagaraComponent = UNiagaraFunctionLibrary::SpawnSystemAtLocation(
				this,
				TileStateConfig.TileStateVisualEffect.Get(),
				EffectLocation,
				EffectRotation,
				EffectScale,
				TileStateConfig.bAutoDestroyVisualEffect,
				TileStateConfig.bAutoActivateVisualEffect,
				TileStateConfig.VisualEffectPoolingMethod,
				TileStateConfig.bPreCullCheckVisualEffect
			);
				Editor_TileActiveNiagaraComponents.Add(NiagaraComponent);
			}));
		}
	}
}

void ACD_Grid::Editor_SpawnActorsOnTile(const FVector& InTileLocation, TArray<FCD_ActorToSpawn>& InActorsToSpawn)
{
	if (InActorsToSpawn.IsEmpty()) return;
	
	for (const auto& ActorToSpawn : InActorsToSpawn)
	{
		if (TSoftClassPtr<AActor> ActorClass = ActorToSpawn.ActorClassToSpawn; ActorClass.IsValid())
		{
			FTransform ActorSpawnTransform = ActorToSpawn.SpawnedActorTransform;
			ActorSpawnTransform.SetLocation(ActorSpawnTransform.GetLocation() + InTileLocation);
			AActor* SpawnedActor = GetWorld()->SpawnActorDeferred<AActor>(ActorClass.Get(), ActorSpawnTransform, this, nullptr, ESpawnActorCollisionHandlingMethod::AlwaysSpawn);
			SpawnedActor->FinishSpawning(ActorSpawnTransform);
			Editor_TileOccupyingActors.Add(SpawnedActor);
		}
		else
		{
			FStreamableManager& StreamableManager = UAssetManager::GetStreamableManager();
			StreamableManager.RequestAsyncLoad(ActorClass.ToSoftObjectPath(),
			FStreamableDelegate::CreateWeakLambda(this,[this, ActorToSpawn, ActorClass, InTileLocation]()
			{
				FTransform ActorSpawnTransform = ActorToSpawn.SpawnedActorTransform;
				ActorSpawnTransform.SetLocation(ActorSpawnTransform.GetLocation() + InTileLocation); 
				AActor* SpawnedActor = GetWorld()->SpawnActorDeferred<AActor>(ActorClass.Get(), ActorSpawnTransform, this, nullptr, ESpawnActorCollisionHandlingMethod::AlwaysSpawn);
				SpawnedActor->FinishSpawning(ActorSpawnTransform);
				Editor_TileOccupyingActors.Add(SpawnedActor);
			}));
		}
	}
}

void ACD_Grid::Server_InitializeGridData()
{
	if (!HasAuthority()) return;
	
	FCD_GridConfiguration FoundConfig = GridRules->GetGridConfigurationByTag(CurrentGridConfigurationTag);
	
	FIntPoint GridSize = FoundConfig.GridSize;
	FVector TilesSpacing = FoundConfig.TilesSpacing;
	FVector GridTilesSize = FoundConfig.GridTilesSize;
	FRotator GridTilesRotation = FoundConfig.TilesRotation;
	int32 RedTeamColumns = FoundConfig.RedTeamColumns;
	int32 GridSegments = FoundConfig.GridSegments;
	FGameplayTag DefaultTileStateTag = FoundConfig.DefaultTileStateTag;
	FGameplayTag NeutralTeamTag = FoundConfig.NeutralTeamTag;
	FGameplayTag BlueTeamTag = FoundConfig.BlueTeamTag;
	FGameplayTag RedTeamTag = FoundConfig.RedTeamTag;
	TMap<FIntPoint, FCD_TileConfiguration> TileConfigurations = FoundConfig.TilesConfig;
	
	TArray<FCD_GridTileEntry> CachedGridTileArray = GridTilesArray.GetAllTiles();
	CachedGridTileArray.Reset(GridSize.X * GridSize.Y);
	Server_TilesWithActiveStateEffects.Reset();
	
	for (int32 X = 0; X < GridSize.X; ++X)
	{
		for (int32 Y = 0; X < GridSize.Y; ++Y)
		{
			const FVector TileLocation = GetActorLocation() + FVector(X * TilesSpacing.X, Y * TilesSpacing.Y, TilesSpacing.Z);
			const FVector TileSize = GridTilesSize;
			FTransform InstanceTransform(GridTilesRotation.Quaternion(), TileLocation, TileSize);
			const FIntPoint TileCoordinates(X, Y);
			UInstancedStaticMeshComponent* TargetISMC = X < GridSize.X / GridSegments ? ISMC_TeamRed : ISMC_TeamBlue;
			const int32 InstanceIndex = TargetISMC->AddInstance(InstanceTransform);
			
			FCD_GridTileEntry NewTile;
			NewTile.TileIndex = InstanceIndex;
			NewTile.GridCoordinates = TileCoordinates;
			NewTile.WorldLocation = TileLocation;
			NewTile.OwningISMC = TargetISMC;

			if (TileConfigurations.Contains(TileCoordinates))
			{
				for (FCD_TileConfiguration TileConfiguration = TileConfigurations.FindRef(InstanceIndex);
					const auto& StateTag : TileConfiguration.TileTags)
				{
					NewTile.TileTags.AddTag(StateTag);
				}
			}
			else
			{
				NewTile.TileTags.AddTag(DefaultTileStateTag);
				
				if (X < RedTeamColumns)
				{
					NewTile.OriginalTeamTag = RedTeamTag;
					NewTile.TileTags.AddTag(RedTeamTag);
				}
				else if (X >= GridSize.X - RedTeamColumns)
				{
					NewTile.OriginalTeamTag = BlueTeamTag;
					NewTile.TileTags.AddTag(BlueTeamTag);
				}
				else
				{
					NewTile.OriginalTeamTag = NeutralTeamTag;
					NewTile.TileTags.AddTag(NeutralTeamTag);
				}
			}

			GridTilesArray.AddTileEntry(NewTile);
		}
	}
}

void ACD_Grid::Server_CheckActiveTileStates()
{
	 if (!HasAuthority()) return;

    const float CurrentTime = GetWorld()->GetTimeSeconds();
    TArray<FIntPoint> TilesThatHadExpirations; // Track which tiles had *any* state expire

	// Iterate over all tiles that have any active timed states
    for (auto It = Server_TilesWithActiveStateEffects.CreateIterator(); It; ++It)
    {
        FIntPoint CurrentCoords = It.Key();
        TArray<FCD_ActiveTileStateInfo>& ActiveStatesOnTile = It.Value().ActiveStatesArray;

        bool bStateExpiredOnThisTile = false;
        for (int32 i = ActiveStatesOnTile.Num() - 1; i >= 0; --i)
        {
            if (CurrentTime >= ActiveStatesOnTile[i].ExpirationTime)
            {
                FGameplayTag ExpiredTag = ActiveStatesOnTile[i].ExpiringStateTag;
                ActiveStatesOnTile.RemoveAt(i);
                
                GridTilesArray.Server_RemoveTagFromTile(CurrentCoords, ExpiredTag);
                UE_LOG(LogTemp, Log, TEXT("State %s expired on tile %s"), *ExpiredTag.ToString(), *CurrentCoords.ToString());
                bStateExpiredOnThisTile = true;
            }
        }

        if (bStateExpiredOnThisTile)
        {
            TilesThatHadExpirations.AddUnique(CurrentCoords);
        }

        if (ActiveStatesOnTile.IsEmpty())
        {
            It.RemoveCurrent(); 
        }
    }
	
    // After processing all expirations, check tiles that need default state evaluation
    for (const FIntPoint& Coords : TilesThatHadExpirations)
    {
	    if (const FCD_GridTileEntry* Tile = GridTilesArray.GetTileByGridCoordinates(Coords))
        {
            bool bHasAnyConfiguredStateTag = false;
            // Check against all *configured significant* states (from TileStatesMap)
            for (const auto& StateConfigPair : TileStatesMap)
            {
                if (Tile->TileTags.HasTag(StateConfigPair.Key))
                {
                    bHasAnyConfiguredStateTag = true;
                    break;
                }
            }
            // Also consider if any *other* timed states are still active on this tile (even if not in TileStatesMap, though less likely)
            if (!bHasAnyConfiguredStateTag && Server_TilesWithActiveStateEffects.Contains(Coords))
            {
                 if (Server_TilesWithActiveStateEffects.Find(Coords)->ActiveStatesArray.Num() > 0)
                 {
                    bHasAnyConfiguredStateTag = true; // There's still some timed effect active
                 }
            }

            if (FGameplayTag DefaultTileStateTag = GridRules->GetGridConfigurationByTag(CurrentGridConfigurationTag).DefaultTileStateTag;
            	!bHasAnyConfiguredStateTag && DefaultTileStateTag.IsValid() && !Tile->HasTag(DefaultTileStateTag))
            {
                 GridTilesArray.AddTagToTile(Coords, DefaultTileStateTag);
                 UE_LOG(LogTemp, Log, TEXT("Reverted tile %s to default state %s after expirations."), *Coords.ToString(), *DefaultTileStateTag.ToString());
            }
        }
    }
}

void ACD_Grid::Server_AddActiveTimedState(const FGameplayTag& InStateTag, const TWeakObjectPtr<AActor>& InInstigator,
	const FIntPoint& InCoordinates, const int32 TileIndex, const float InDuration)
{
	if (!HasAuthority() || !InStateTag.IsValid() || InDuration <= 0.f) return;

	/*This function now assumes that if a state with the same StateTag exists, its duration should have been refreshed
	by Server_ApplyTileStateLogic. This function is purely for adding a *new* tracking entry
	if one didn't exist for refreshing.*/

	float ExpirationTime = GetWorld()->GetTimeSeconds() + InDuration;
	const FCD_ActiveTileStateInfo NewTimedState(InStateTag, InInstigator, InCoordinates, TileIndex, ExpirationTime);
    
	Server_TilesWithActiveStateEffects.FindOrAdd(InCoordinates).ActiveStatesArray.Add(NewTimedState);
	UE_LOG(LogTemp, Log, TEXT("Added new timed state %s on tile %s, duration %f"), *InStateTag.ToString(),
		*InCoordinates.ToString(), InDuration);
}

void ACD_Grid::Server_ClearSpecificTimedState(const FIntPoint& InCoordinates, const FGameplayTag& InStateTagToRemove)
{
	if (!HasAuthority()) return;

	if (TArray<FCD_ActiveTileStateInfo> ActiveStates = Server_TilesWithActiveStateEffects.Find(InCoordinates)->ActiveStatesArray;
		!ActiveStates.IsEmpty())
	{
		for (int32 i = ActiveStates.Num() - 1; i >= 0; --i)
		{
			if (ActiveStates[i].ExpiringStateTag.MatchesTagExact(InStateTagToRemove))
			{
				ActiveStates.RemoveAt(i);
				UE_LOG(LogTemp, Log, TEXT("Cleared specific timed state %s from tracking on tile %s"),
					*InStateTagToRemove.ToString(), *InCoordinates.ToString());
				break; // Assuming only one instance of a specific timed state tag per tile
			}
		}
		if (ActiveStates.IsEmpty())
		{
			Server_TilesWithActiveStateEffects.Remove(InCoordinates);
		}
	}
}

/*void ACD_Grid::Server_ApplyStateToTiles_Implementation(const FCD_GridTileEntry* InTileEntry, const FCD_TileState& InTileState,  AActor* InInstigator)
{
	if (!HasAuthority() || !InTileEntry || !InTileState.StateTag.IsValid()) return;

    const FIntPoint TileCoordinates = InTileEntry->GridCoordinates;
	
    // 1. Handle TagsToRemoveOnApply specified by the new state
    if (InTileState.TagsToRemoveOnApplication.IsValid())
    {
        const FGameplayTagContainer CurrentTagsCopy = InTileEntry->TileTags; // Iterate on a copy
        for (const FGameplayTag& TagToRemove : InTileState.TagsToRemoveOnApplication)
        {
            if (CurrentTagsCopy.HasTag(TagToRemove))
            {
                GridTilesArray.Server_RemoveTagFromTile(TileCoordinates, TagToRemove);
                // Also remove from timed effects tracking if it was a timed state
                Server_ClearSpecificTimedState(TileCoordinates, TagToRemove);
            }
        }
    }

    // 2. Handle bClearsOtherTimedStates
    if (InTileState.bClearsOtherTimedStates)
    {
        // Iterate through all currently tracked timed states on this tile
        if (TArray<FCD_ActiveTileStateInfo> ActiveStates = Server_TilesWithActiveStateEffects.Find(TileCoordinates)->ActiveStatesArray;
		!ActiveStates.IsEmpty())
        {
            for (int32 i = ActiveStates.Num() - 1; i >= 0; --i)
            {
	            // Don't clear self if we are just refreshing
                if (const FCD_ActiveTileStateInfo& ExistingTimedState = ActiveStates[i];
                	ExistingTimedState.ExpiringStateTag != InTileState.StateTag)
                {
                    GridTilesArray.Server_RemoveTagFromTile(TileCoordinates, ExistingTimedState.ExpiringStateTag);
                    ActiveStates.RemoveAt(i); // Remove from tracking
                }
            }
            if (ActiveStates.IsEmpty())
            {
                Server_TilesWithActiveStateEffects.Remove(TileCoordinates);
            }
        }
    }
    
    // 3. Add or Refresh the new state
    bool bWasExistingTimedState = false;
    if (InTileState.bHasDuration)
    {
        // Check if this specific timed state is already active to refresh its duration
    	if (TArray<FCD_ActiveTileStateInfo> ActiveStates = Server_TilesWithActiveStateEffects.Find(TileCoordinates)->ActiveStatesArray;
		!ActiveStates.IsEmpty())
        {
            for (FCD_ActiveTileStateInfo& ExistingTimedState : ActiveStates)
            {
                if (ExistingTimedState.ExpiringStateTag.MatchesTagExact(InTileState.StateTag))
                {
                    ExistingTimedState.ExpirationTime = GetWorld()->GetTimeSeconds() + InTileState.StateDuration;
                    bWasExistingTimedState = true;
                    UE_LOG(LogTemp, Log, TEXT("Refreshed duration for state %s on tile %s"),
                    	*InTileState.StateTag.ToString(), *TileCoordinates.ToString());
                    break;
                }
            }
        }
    }

    // If it's a new application of this state (or not a timed state being refreshed)
    if (!bWasExistingTimedState)
    {
        GridTilesArray.AddTagToTile(TileCoordinates, InTileState.StateTag);
        if (InTileState.bHasDuration)
        {
            Server_AddActiveTimedState(InTileState.StateTag, InInstigator, TileCoordinates,
            	InTileEntry->TileIndex, InTileState.StateDuration);
        }
    }
    else if (bWasExistingTimedState && !InTileEntry->HasTag(InTileState.StateTag))
    {
        // This can happen if the tag was removed by another effect but the timed entry was still there. Re-add the tag.
        GridTilesArray.AddTagToTile(TileCoordinates, InTileState.StateTag);
    }


    // 4. Ensure Default state is handled correctly
    // If the new state is NOT the default state, remove the default state tag.
    if (InTileState.StateTag != DefaultTileStateTag && InTileEntry->HasTag(DefaultTileStateTag))
    {
        GridTilesArray.Server_RemoveTagFromTile(TileCoordinates, DefaultTileStateTag);
    }
    // If, after all changes, NO "Tile.State.*" tags are present, add the default one back.
    bool bHasAnyConfiguredStateTag = false;
    for (const auto& StateConfigPair : TileStatesMap)
    {
        if (InTileEntry->HasTag(StateConfigPair.Key))
        {
            bHasAnyConfiguredStateTag = true;
            break;
        }
    }
	
    if (!bHasAnyConfiguredStateTag && DefaultTileStateTag.IsValid())
    {
        GridTilesArray.AddTagToTile(TileCoordinates, DefaultTileStateTag);
    }

	if (InTileState.bAppliesGameplayEffect && InTileState.AffectToApply.IsValid() && IsTileOccupied(InTileEntry->GridCoordinates))
	{
		// TODO: Potentially requires updating later in case a better way to apply and remove Gameplay Effects is made
		Server_ApplyGameplayEffectToActorOnTile(InTileEntry->OccupyingActors, InTileState.AffectToApply, 1);
	}

	if (InTileState.bGrantsGameplayAbility && InTileState.AbilityToGive.IsValid() && IsTileOccupied(InTileEntry->GridCoordinates))
	{
		// TODO: Potentially requires updating later in case a better way to apply and remove Gameplay Abilities is made
		Server_GrantAbilityToActorOnTile(InTileEntry->OccupyingActors, InTileState.AbilityToGive, 1);
	}
}*/

void ACD_Grid::Server_ApplyGameplayEffectToActorOnTile(TArray<TWeakObjectPtr<AActor>> InOccupyingActors,
	const TSoftClassPtr<UGameplayEffect>& InGameplayEffectClass, const float InGameplayEffectLevel) const
{
	if (!HasAuthority() || InGameplayEffectClass.IsNull()) return;

	// Load the GameplayEffect class
	const TSubclassOf<UGameplayEffect> GameplayEffectClass = InGameplayEffectClass.LoadSynchronous();
	if (!GameplayEffectClass)
	{
		UE_LOG(LogTemp, Warning, TEXT("Failed to load GameplayEffectClass from TSoftClassPtr: %s"), *InGameplayEffectClass.ToString());
		return;
	}

	for (const auto& OccupyingActor: InOccupyingActors)
	{
		if (OccupyingActor.IsValid())
		{
			if (UAbilitySystemComponent* ASC = UAbilitySystemBlueprintLibrary::GetAbilitySystemComponent(OccupyingActor.Get()))
			{
				FGameplayEffectContextHandle ContextHandle = ASC->MakeEffectContext();
				ContextHandle.AddSourceObject(this);

				if (const FGameplayEffectSpecHandle SpecHandle = ASC->MakeOutgoingSpec(GameplayEffectClass, InGameplayEffectLevel, ContextHandle); SpecHandle.IsValid())
				{
					ASC->ApplyGameplayEffectSpecToSelf(*SpecHandle.Data.Get());
					UE_LOG(LogTemp, Log, TEXT("Applied GE %s to actor %s on tile due to state."),
						*GameplayEffectClass->GetName(), *OccupyingActor->GetName());
				}
			}
		}
	}
}

void ACD_Grid::Server_RemoveGameplayEffectFromActorOnTile(TArray<TWeakObjectPtr<AActor>> InOccupyingActors,
	const TSoftClassPtr<UGameplayEffect>& InGameplayEffectSoftClass) const
{
	if (!HasAuthority() || InGameplayEffectSoftClass.IsNull()) return;

	// Load the GameplayEffect class
	const TSubclassOf<UGameplayEffect> GameplayEffectClass = InGameplayEffectSoftClass.LoadSynchronous();
	
	if (!GameplayEffectClass)
	{
		UE_LOG(LogTemp, Warning, TEXT("Failed to load GameplayEffectClass for removal from TSoftClassPtr: %s"), *InGameplayEffectSoftClass.ToString());
		return;
	}

	for (const auto& OccupyingActor : InOccupyingActors)
	{
		if (UAbilitySystemComponent* ASC = UAbilitySystemBlueprintLibrary::GetAbilitySystemComponent(OccupyingActor.Get()))
		{
			FGameplayEffectQuery Query;
			Query.EffectDefinition = GameplayEffectClass;
			if (const int32 RemovedCount = ASC->RemoveActiveEffects(Query); RemovedCount > 0)
			{
				UE_LOG(LogTemp, Log, TEXT("Removed %d stacks/instances of GE %s from actor %s."), RemovedCount, *GameplayEffectClass->GetName(), *OccupyingActor->GetName());
			}
		}
	}
}

void ACD_Grid::Server_GrantAbilityToActorOnTile(TArray<TWeakObjectPtr<AActor>> InOccupyingActors,
	TSoftClassPtr<UGameplayAbility> GameplayAbilitySoftClass, int32 AbilityLevel)
{
	if (!HasAuthority() || GameplayAbilitySoftClass.IsNull()) return;

	// Load the GameplayAbility class
	TSubclassOf<UGameplayAbility> GameplayAbilityClass = GameplayAbilitySoftClass.LoadSynchronous();
	if (!GameplayAbilityClass)
	{
		UE_LOG(LogTemp, Warning, TEXT("Failed to load GameplayAbilityClass from TSoftClassPtr: %s"), *GameplayAbilitySoftClass.ToString());
		return;
	}

	for (const auto& OccupyingActor : InOccupyingActors)
	{
		if (UAbilitySystemComponent* ASC = UAbilitySystemBlueprintLibrary::GetAbilitySystemComponent(OccupyingActor.Get()))
		{
			if (ASC->FindAbilitySpecFromClass(GameplayAbilityClass))
			{
				UE_LOG(LogTemp, Log, TEXT("Actor %s already has ability %s. Not re-granting."), *OccupyingActor->GetName(), *GameplayAbilityClass->GetName());
				// Depending on design, you might want to refresh it or do nothing. For now, do nothing if already present.
				return;
			}

			FGameplayAbilitySpec AbilitySpec(GameplayAbilityClass, AbilityLevel);
			AbilitySpec.SourceObject = this; 

			ASC->GiveAbility(AbilitySpec);
			UE_LOG(LogTemp, Log, TEXT("Granted ability %s to actor %s on tile due to state."), *GameplayAbilityClass->GetName(), *OccupyingActor->GetName());
		}
	}
}

/*
void ACD_Grid::Server_SpawnActorOnTile_Implementation(const FIntPoint& InGridCoordinates,
	const TSoftClassPtr<AActor>& InActorClass, const FTransform& InActorTransform)
{
	return SpawnActorOnTile(InGridCoordinates, InActorClass, InActorTransform);
}
*/

void ACD_Grid::Server_SetGridConfiguration_Implementation(const FGameplayTag& InGridConfigurationTag)
{
	SetGridConfiguration(InGridConfigurationTag);
}

void ACD_Grid::Server_ApplyStateToTiles_Implementation(const FGameplayTag& InStateToApply,
                                                       const TArray<FIntPoint>& InTargetCoordinates, AActor* InInstigator)
{
	ApplyStateToTiles(InStateToApply, InTargetCoordinates, InInstigator);
}

void ACD_Grid::Client_HandleTileAdded(const FCD_GridTileEntry& InAddedTile)
{
	FCD_GridTileEntry* ClientTileEntry = GetTileAtCoordinates(InAddedTile.GridCoordinates);
	const FCD_GridConfiguration GridConfig = GridRules->GetGridConfigurationByTag(CurrentGridConfigurationTag);
	if (!ClientTileEntry->IsValid()) 
	{
		// Log warning if local entry not found (should not happen if array is consistent)
		UE_LOG(LogTemp, Warning, TEXT("Client_HandleTileAddedDelegate: Could not find local tile entry for %s "
								"or ISMC/Mesh invalid."), *InAddedTile.GridCoordinates.ToString());
		return;
	}

	const FVector GridCenterOffset = FVector((GridConfig.GridSize.X - 1) * GridConfig.TilesSpacing.X * 0.5f, 
											 (GridConfig.GridSize.Y - 1) * GridConfig.TilesSpacing.Y * 0.5f, 
											 0.f);
	ClientTileEntry->WorldLocation = FVector(ClientTileEntry->GridCoordinates.X * GridConfig.TilesSpacing.X, 
												 ClientTileEntry->GridCoordinates.Y * GridConfig.TilesSpacing.Y, 
												 GridConfig.TilesSpacing.Z) - GridCenterOffset;
	
	const FTransform InstanceTransform(FRotator::ZeroRotator, ClientTileEntry->WorldLocation, GridConfig.GridTilesSize);
	const int32 NewInstanceIndex = ISMC_TeamBlue->AddInstance(InstanceTransform);
	
	ClientTileEntry->TileIndex = NewInstanceIndex;

	Client_UpdateTileVisuals(ClientTileEntry);
}

void ACD_Grid::Client_HandleTileChanged(const FCD_GridTileEntry& InChangedTile)
{
	if (const FCD_GridTileEntry* TileEntry = GetTileAtCoordinates(InChangedTile.GridCoordinates))
	{
		if (TileEntry->IsValid())
		{
			Client_UpdateTileVisuals(TileEntry);
		}
		else
		{
			Client_HandleTileAdded(InChangedTile);
		}
	}
}

void ACD_Grid::Client_HandleTileRemoved(const FCD_GridTileEntry& InRemovedTile)
{
	if (FCD_GridTileEntry* TileEntry = GetTileAtCoordinates(InRemovedTile.GridCoordinates);
		TileEntry->TileIndex != INDEX_NONE)
	{
		ISMC_TeamBlue->RemoveInstance(TileEntry->TileIndex);
		TileEntry->TileIndex = INDEX_NONE; 
	}
	
	// Clear any Niagara effects for the removed tile
	for (const auto& TagConfigPair : TileStatesMap)
	{
		Client_ClearNiagaraEffectForState(InRemovedTile.GridCoordinates, TagConfigPair.Key);
	}
	
	ActiveTileNiagaraComponents.Remove(InRemovedTile.GridCoordinates);
}

void ACD_Grid::Client_UpdateTileVisuals(const FCD_GridTileEntry* InTileData)
{
	if (!InTileData->IsValid() || InTileData->TileIndex == INDEX_NONE || !IsValid(ISMC_TeamBlue)) return;

	for (const auto& TagConfigPair : TileStatesMap)
	{
		if (InTileData->HasTag(TagConfigPair.Key))
		{
			const FCD_TileState& StateConfig = TagConfigPair.Value;
			
			if (StateConfig.bChangesMaterial)
			{
				
			}
			
			if (StateConfig.bHasVisualEffect)
			{
				Client_SpawnNiagaraEffectForState(*InTileData, StateConfig);
			}
			else // If this state *doesn't* have a visual effect, ensure any previous one for its tag is cleared
			{
				Client_ClearNiagaraEffectForState(InTileData->GridCoordinates, TagConfigPair.Key);
			}
		}
		else // Tag is NOT present, ensure any Niagara effect for THIS specific state tag is cleared
		{
			Client_ClearNiagaraEffectForState(InTileData->GridCoordinates, TagConfigPair.Key);
		}
	}
}

void ACD_Grid::Client_SpawnNiagaraEffectForState(const FCD_GridTileEntry& InTileEntry, const FCD_TileState& InTileState)
{
	if (InTileEntry.IsValid() || !InTileState.TileStateVisualEffect.IsValid()) return;

	TMap<FGameplayTag, TWeakObjectPtr<UNiagaraComponent>>& TileEffectsMap = ActiveTileNiagaraComponents.FindOrAdd(InTileEntry.GridCoordinates);
    
	// If an effect for this specific state tag already exists, don't respawn
	if (TileEffectsMap.Contains(InTileState.StateTag) && TileEffectsMap[InTileState.StateTag].IsValid()) return;
	
	if (InTileState.TileStateVisualEffect.IsValid())
	{
		Client_OnNiagaraSystemLoaded(InTileEntry.GridCoordinates, InTileState);
	}
	else
	{
		FStreamableManager& StreamableManager = UAssetManager::GetStreamableManager();
		StreamableManager.RequestAsyncLoad(InTileState.TileStateVisualEffect.ToSoftObjectPath(),
		FStreamableDelegate::CreateWeakLambda(this,[this, InTileEntry, InTileState]()
		{
			Client_OnNiagaraSystemLoaded(InTileEntry.GridCoordinates, InTileState);
		}));
	}
}

void ACD_Grid::Client_ClearNiagaraEffectForState(const FIntPoint& InCoordinates, const FGameplayTag& InStateTag)
{
	if (TMap<FGameplayTag, TWeakObjectPtr<UNiagaraComponent>>* TileEffectsMap = ActiveTileNiagaraComponents.Find(InCoordinates))
	{
		if (const TWeakObjectPtr<UNiagaraComponent>* NiagaraComponent = TileEffectsMap->Find(InStateTag))
		{
			if (NiagaraComponent->IsValid())
			{
				if (UNiagaraComponent* CachedNiagaraComponent = NiagaraComponent->Get(); CachedNiagaraComponent->IsActive())
				{
					CachedNiagaraComponent->Deactivate();
				}
			}
			TileEffectsMap->Remove(InStateTag);
		}
		if (TileEffectsMap->IsEmpty())
		{
			ActiveTileNiagaraComponents.Remove(InCoordinates);
		}
	}
}

void ACD_Grid::Client_OnNiagaraSystemLoaded(const FIntPoint& InCoordinates, const FCD_TileState& InTileState)
{
	// Verify tile still exists and needs this effect (tags might have changed during async load)
	const FCD_GridTileEntry* TileData = GridTilesArray.GetTileByGridCoordinates(InCoordinates);
	if (!TileData || !TileData->HasTag(InTileState.StateTag) || !InTileState.TileStateVisualEffect.Get()) return;

	TMap<FGameplayTag, TWeakObjectPtr<UNiagaraComponent>>& TileEffectsMap = ActiveTileNiagaraComponents.FindOrAdd(InCoordinates);
    
	// Double check if another async load for the same effect completed first
	if (TileEffectsMap.Contains(InTileState.StateTag) && TileEffectsMap[InTileState.StateTag].IsValid())
	{
		return;
	}

	const FVector EffectLocation = GetActorTransform().TransformPosition(TileData->WorldLocation) + InTileState.VisualEffectTransform.GetLocation();
	const FRotator EffectRotation = InTileState.VisualEffectTransform.Rotator(); // Assuming world space rotation for effect
	const FVector EffectScale = InTileState.VisualEffectTransform.GetScale3D();

	UNiagaraComponent* NiagaraComponent = UNiagaraFunctionLibrary::SpawnSystemAtLocation(
		this,
		InTileState.TileStateVisualEffect.Get(),
		EffectLocation,
		EffectRotation,
		EffectScale,
		InTileState.bAutoDestroyVisualEffect,
		InTileState.bAutoActivateVisualEffect,
		InTileState.VisualEffectPoolingMethod,
		InTileState.bPreCullCheckVisualEffect
	);

	if (IsValid(NiagaraComponent))
	{
		TileEffectsMap.Add(InTileState.StateTag, NiagaraComponent);
	}
}
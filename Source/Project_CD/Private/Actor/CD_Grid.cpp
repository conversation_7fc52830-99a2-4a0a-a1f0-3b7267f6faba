// Copyright Horizon Seeker Studios


#include "Actor/CD_Grid.h"

#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "Components/InstancedStaticMeshComponent.h"
#include "Engine/AssetManager.h"
#include "Engine/StreamableManager.h"
#include "Net/UnrealNetwork.h"


ACD_Grid::ACD_Grid() : GridTilesArray(this)
{
	PrimaryActorTick.bCanEverTick = false;
	PrimaryActorTick.bStartWithTickEnabled = false;
	bReplicates = true;

	InstancedStaticMeshComponent = CreateDefaultSubobject<UInstancedStaticMeshComponent>(TEXT("InstancedStaticMeshComponent"));
	InstancedStaticMeshComponent->SetIsReplicated(true);
	InstancedStaticMeshComponent->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
	InstancedStaticMeshComponent->SetCollisionObjectType(ECC_WorldStatic);
	InstancedStaticMeshComponent->SetCollisionResponseToChannel(ECC_Pawn, ECR_Overlap);
	InstancedStaticMeshComponent->SetCollisionResponseToChannel(ECC_Camera, ECR_Ignore);
	InstancedStaticMeshComponent->NumCustomDataFloats = 10;
	RootComponent = InstancedStaticMeshComponent;
}

void ACD_Grid::BeginPlay()
{
	Super::BeginPlay();

	if (!HasAuthority())
	{
		GridTilesArray.DirtyTileArrayDelegate.AddUObject(this, &ThisClass::Client_HandleTileDataChanged);
	}
	
	if (HasAuthority())
	{
		Server_InitializeGridData();
	}
}

void ACD_Grid::OnConstruction(const FTransform& Transform)
{
	Super::OnConstruction(Transform);
	SpawnGridVisuals();
}

FCD_GridTileEntry* ACD_Grid::GetTileAtCoordinates(const FIntPoint& InGridCoordinates)
{
	return GridTilesArray.GetTileByGridCoordinates(InGridCoordinates);
}

TArray<FCD_GridTileEntry> ACD_Grid::GetTilesByState(const FGameplayTag& InStateTag)
{
	return GridTilesArray.GetTilesByTag(InStateTag);
}

bool ACD_Grid::IsTileOccupied(const FIntPoint& InGridPosition)
{
	if (const FCD_GridTileEntry* TileEntry = GetTileAtCoordinates(InGridPosition))
	{
		return TileEntry->IsTileOccupied();
	}
	return false;
}

void ACD_Grid::SpawnGridVisuals()
{
	UStaticMesh* CachedStaticMesh = InstancedStaticMeshComponent->GetStaticMesh();
	if (!IsValid(CachedStaticMesh) || !IsValid(TileMaterial)) return;
	
	InstancedStaticMeshComponent->ClearInstances();
	InstancedStaticMeshComponent->SetStaticMesh(CachedStaticMesh);
	InstancedStaticMeshComponent->SetMaterial(0,TileMaterial);

	TilesToInitialize.Reset(GridSize.X * GridSize.Y);
	
	for (int32 Y = 0; Y < GridSize.Y; ++Y)
	{
		for (int32 X = 0; X < GridSize.X; ++X)
		{
			const FVector TileLocation = GetActorLocation() + FVector(X * TileSpacing.X, Y * TileSpacing.Y, 0);
			const FVector TileSize = GridTileSize;
			FTransform InstanceTransform(FRotator::ZeroRotator, TileLocation, TileSize);
			const int32 InstanceIndex = InstancedStaticMeshComponent->AddInstance(InstanceTransform);
			const FIntPoint GridCoordinates(X, Y);
			
			FCD_GridTileEntry NewTile;
			NewTile.TileIndex = InstanceIndex;
			NewTile.GridCoordinates = GridCoordinates;
			NewTile.WorldLocation = TileLocation;
			NewTile.TileTags.AddTag(DefaultTileStateTag);
			
			if (X < RedTeam.NumberOfColumns)
			{
				InstancedStaticMeshComponent->SetCustomData(InstanceIndex, RedTeam.CustomDataFloats, true);
				NewTile.TileTags.AddTag(RedTeam.TeamAffiliationTag);
			}
			else if (X >= GridSize.X - RedTeam.NumberOfColumns)
			{
				InstancedStaticMeshComponent->SetCustomData(InstanceIndex, BlueTeam.CustomDataFloats, true);
				NewTile.TileTags.AddTag(BlueTeam.TeamAffiliationTag);
			}
			else
			{
				InstancedStaticMeshComponent->SetCustomData(InstanceIndex, NeutralTeam.CustomDataFloats, true);
				NewTile.TileTags.AddTag(NeutralTeam.TeamAffiliationTag);
			}
			
			TilesToInitialize.Add(NewTile);
		}
	}
}

void ACD_Grid::ResetTileStateToDefault()
{
	if (FTimerManager& TimerManager = GetWorld()->GetTimerManager(); TilesWithActiveEffects.IsEmpty() &&
		TimerManager.IsTimerActive(TileStateResetHandle))
	{
		TimerManager.ClearTimer(TileStateResetHandle);
		return;
	}

	const float CurrentTime = GetWorld()->GetTimeSeconds();
	
	for (const auto& ActiveTileStateInfo : TilesWithActiveEffects)
	{
		if (!ActiveTileStateInfo.bHasDuration) continue;

		//TODO: Add reset tile state logic here
	}
}

void ACD_Grid::SpawnTileStateVisualEffect(const TSoftObjectPtr<UNiagaraSystem>& InNiagaraSystem,
	const FCD_GridTileEntry& InTileEntry, const FCD_TileState& InTileState)
{
	if (UNiagaraSystem* NiagaraSystemLoaded = InNiagaraSystem.Get(); IsValid(NiagaraSystemLoaded))
	{
		const FRotator NiagaraSystemRotation = InTileState.VisualEffectTransform.Rotator();
		const FVector NiagaraSystemLocation = InTileEntry.WorldLocation + InTileState.VisualEffectTransform.GetLocation();
		const FVector NiagaraSystemScale = InTileState.VisualEffectTransform.GetScale3D();
		const bool bAutoDestroy = InTileState.bAutoDestroyVisualEffect;
		const bool bAutoActivate = InTileState.bAutoActivateVisualEffect;
		const bool bPreCullCheck = InTileState.bPreCullCheckVisualEffect;
		const ENCPoolMethod PoolingMethod = InTileState.VisualEffectPoolingMethod;
		
		UNiagaraComponent* NiagaraComponent = UNiagaraFunctionLibrary::SpawnSystemAtLocation
		(this, NiagaraSystemLoaded, NiagaraSystemLocation,
			NiagaraSystemRotation, NiagaraSystemScale, bAutoDestroy,
			bAutoActivate, PoolingMethod, bPreCullCheck);

		FCD_ActiveTileStateInfo ActiveTileStateInfo;
		ActiveTileStateInfo.TileIndex = InTileEntry.TileIndex;
		ActiveTileStateInfo.GridCoordinates = InTileEntry.GridCoordinates;
		ActiveTileStateInfo.ActiveNiagaraComponent = NiagaraComponent;

		if (InTileState.bHasDuration)
		{
			ActiveTileStateInfo.bHasDuration = InTileState.bHasDuration;
			ActiveTileStateInfo.Duration = InTileState.StateDuration;
		}

		TilesWithActiveEffects.Add(ActiveTileStateInfo);
	}
}

void ACD_Grid::UpdateTileInstanceVisuals(TArray<FCD_GridTileEntry>& InDirtyTiles)
{
	for (const auto& TileEntry : InDirtyTiles)
	{
		for (const auto& TileState : TileStatesList)
		{
			if (TileEntry.HasTag(TileState.Key))
			{
				if (TileState.Value.bChangesMaterial)
				{
					InstancedStaticMeshComponent->SetCustomData(TileEntry.TileIndex, TileState.Value.CustomDataFloats, true);
					FCD_ActiveTileStateInfo ActiveTileStateInfo;
					ActiveTileStateInfo.TileIndex = TileEntry.TileIndex;
					ActiveTileStateInfo.GridCoordinates = TileEntry.GridCoordinates;

					if (TileState.Value.bHasDuration)
					{
						ActiveTileStateInfo.bHasDuration = TileState.Value.bHasDuration;
						ActiveTileStateInfo.Duration = TileState.Value.StateDuration;
					}

					TilesWithActiveEffects.Add(ActiveTileStateInfo);
				}

				if (TileState.Value.bHasVisualEffect)
				{
					if (TSoftObjectPtr<UNiagaraSystem> NiagaraSystem = TileState.Value.TileStateVisualEffect;
						!NiagaraSystem.IsValid())
					{
						FStreamableManager& StreamableManager = UAssetManager::GetStreamableManager();
						StreamableManager.RequestAsyncLoad(NiagaraSystem.ToSoftObjectPath(),
							FStreamableDelegate::CreateWeakLambda(this, [this, NiagaraSystem,
								TileEntry, TileState]()
							{
								SpawnTileStateVisualEffect(NiagaraSystem, TileEntry, TileState.Value);
							}));
					}
					else
					{
						SpawnTileStateVisualEffect(NiagaraSystem, TileEntry, TileState.Value);
					}
				}
			}
		}
	}
}

void ACD_Grid::Client_HandleTileDataChanged(TArray<FCD_GridTileEntry>& InDirtyTiles)
{
	UpdateTileInstanceVisuals(InDirtyTiles);
}

void ACD_Grid::Server_InitializeGridData_Implementation()
{
	if (!HasAuthority()) return;

	TArray<FCD_GridTileEntry> CachedGridTileArray = GridTilesArray.GetAllTiles();
	CachedGridTileArray.Empty(GridSize.X * GridSize.Y);

	for (const auto& TileEntry : TilesToInitialize)
	{
		GridTilesArray.AddTileEntry(TileEntry);
	}
}

void ACD_Grid::GetLifetimeReplicatedProps(TArray<class FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);

	DOREPLIFETIME(ACD_Grid, GridTilesArray);
}

void ACD_Grid::UpdateTilesState(const FGameplayTag& InStateTag, const TArray<FIntPoint>& InGridCoordinates)
{
	if (!HasAuthority())
	{
		Server_UpdateTileState(InStateTag, InGridCoordinates);
		return;
	}

	const FCD_TileState* TileState = TileStatesList.Find(InStateTag);
	if (!TileState) return;

	TArray<FCD_GridTileEntry> TilesToUpdate = GridTilesArray.GetTilesByGridCoordinates(InGridCoordinates);
	
	GridTilesArray.UpdateTileState(TileState, InGridCoordinates);
}

void ACD_Grid::Server_UpdateTileState_Implementation(const FGameplayTag& InStateTag, const TArray<FIntPoint>& InGridCoordinates)
{
	UpdateTilesState(InStateTag, InGridCoordinates);
}



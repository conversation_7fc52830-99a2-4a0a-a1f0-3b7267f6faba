// Copyright Horizon Seeker Studios

#pragma once

#include "CoreMinimal.h"
#include "GameplayTagContainer.h"
#include "NiagaraComponentPoolMethodEnum.h"
#include "Net/Serialization/FastArraySerializer.h"
#include "CD_Grid_Types.generated.h"

/**
 * 
 */

class ACD_Grid;
class UNiagaraComponent;
class UGameplayAbility;
class UNiagaraSystem;
class UGameplayEffect;

USTRUCT(BlueprintType)
struct FCD_ActiveTileStateInfo
{
	GENERATED_BODY()

	UPROPERTY(BlueprintReadOnly, Transient)
	FGameplayTag ExpiringStateTag;

	UPROPERTY(BlueprintReadOnly, Transient)
	TWeakObjectPtr<AActor> InstigatorActor;
	
	UPROPERTY(BlueprintReadOnly, Transient)
	FIntPoint GridCoordinates = FIntPoint(INDEX_NONE, INDEX_NONE);
	
	UPROPERTY(BlueprintReadOnly, Transient)
	int32 TileIndex = INDEX_NONE;

	UPROPERTY(BlueprintReadOnly, Transient)
	float ExpirationTime = 0.f;
	
	FCD_ActiveTileStateInfo(const FGameplayTag& InTag, const TWeakObjectPtr<AActor>& InInstigator,
		const FIntPoint& InCoords, const int32 InTileIndex, const float InExpirationTime) : ExpiringStateTag(InTag),
	InstigatorActor(InInstigator), GridCoordinates(InCoords), TileIndex(InTileIndex), ExpirationTime(InExpirationTime) {}

	FCD_ActiveTileStateInfo() = default;
};

USTRUCT(BlueprintType)
struct FCD_TileActiveStates
{
	GENERATED_BODY()

	UPROPERTY()
	TArray<FCD_ActiveTileStateInfo> ActiveStatesArray;
};

USTRUCT(BlueprintType)
struct FCD_ActorToSpawn
{
	GENERATED_BODY()
	
	UPROPERTY(EditAnywhere, BlueprintReadOnly)
	TSoftClassPtr<AActor> ActorClassToSpawn = nullptr;

	UPROPERTY(EditAnywhere, BlueprintReadOnly)
	FTransform SpawnedActorTransform = FTransform::Identity;
};

USTRUCT(BlueprintType)
struct FCD_TileConfiguration
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadOnly, meta = (Categories = "Tile.State"))
	FGameplayTagContainer TileTags = FGameplayTagContainer();
	
	UPROPERTY(EditAnywhere, BlueprintReadOnly)
	TArray<FCD_ActorToSpawn> ActorsToSpawnList = TArray<FCD_ActorToSpawn>();
};

USTRUCT(BlueprintType)
struct FCD_GridConfiguration : public FTableRowBase
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadOnly, meta = (Categories = "Grid.Configuration"))
	FGameplayTag GridConfigurationTag = FGameplayTag();

	UPROPERTY(EditAnywhere, BlueprintReadOnly, meta=(Categories = "Tile.State"))
	FGameplayTag DefaultTileStateTag;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, meta = (Categories = "Tile"))
	FGameplayTag NeutralTeamTag;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, meta = (Categories = "Tile"))
	FGameplayTag BlueTeamTag;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, meta = (Categories = "Tile"))
	FGameplayTag RedTeamTag;

	UPROPERTY(EditAnywhere, BlueprintReadOnly)
	int32 RedTeamColumns = 5;

	UPROPERTY(EditAnywhere, BlueprintReadOnly)
	int32 GridSegments = 2;	
	
	UPROPERTY(EditAnywhere, BlueprintReadOnly)
	FIntPoint GridSize = {10, 5};
	
	UPROPERTY(EditAnywhere, BlueprintReadOnly)
	FTransform GridTransform = FTransform();
	
	UPROPERTY(EditAnywhere, BlueprintReadOnly)
	FVector TilesSpacing = FVector(120.f, 120.f, 0.f);

	UPROPERTY(EditAnywhere, BlueprintReadOnly)
	FRotator TilesRotation = FRotator::ZeroRotator;
	
	UPROPERTY(EditAnywhere, BlueprintReadOnly)
	FVector GridTilesSize = FVector(1.f, 1.f, 1.f);
	
	UPROPERTY(EditAnywhere, BlueprintReadOnly)
	float ResetTileStateTimerRate = 10.f;	

	UPROPERTY(EditAnywhere, BlueprintReadOnly)
	TMap<FIntPoint, FCD_TileConfiguration> TilesConfig;
};

USTRUCT(BlueprintType)
struct FCD_MaterialCustomData
{
	GENERATED_BODY()

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly)
	float TargetCustomDataValue = 0.f;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly)
	bool bAnimateCustomDataValue = false;	

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, meta=(EditCondition = "bAnimateCustomDataValue == true" ,EditConditionHides))
	float StartingCustomDataValue = 1.f;
	
	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, meta=(EditCondition = "bAnimateCustomDataValue == true" ,EditConditionHides))
	float CustomDataTransitionSpeed = 0.25f;
};

USTRUCT(BlueprintType)
struct FCD_TileState : public FTableRowBase
{
	GENERATED_BODY()

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, meta = (Categories = "Tile"))
	FGameplayTag StateTag = FGameplayTag();

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, meta = (Categories = "Tile"))
	FGameplayTagContainer TagsToRemoveOnApplication = FGameplayTagContainer();

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly)
	bool bClearsOtherTimedStates = false;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly)
	bool bHasDuration = false;
	
	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, meta=(EditCondition = "bHasDuration == true" ,EditConditionHides))
	float StateDuration = 10.f;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly)
	bool bAppliesGameplayEffect = false;
	
	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, meta=(EditCondition = "bAppliesGameplayEffect == true" ,EditConditionHides))
	TSoftClassPtr<UGameplayEffect> AffectToApply = nullptr;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly)
	bool bGrantsGameplayAbility = false;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, meta=(EditCondition = "bGrantsGameplayAbility == true" ,EditConditionHides))
	TSoftClassPtr<UGameplayAbility> AbilityToGive = nullptr;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly)
	bool bChangesMaterial = false;	
	
	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, meta=(EditCondition = "bChangesMaterial == true",EditConditionHides))
	TMap<int32, FCD_MaterialCustomData> CustomDataFloats = TMap<int32, FCD_MaterialCustomData>();
	
	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly)
	bool bHasVisualEffect = false;
	
	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, meta=(EditCondition = "bHasVisualEffect == true" ,EditConditionHides))
	TSoftObjectPtr<UNiagaraSystem> TileStateVisualEffect = nullptr;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, meta=(EditCondition = "bHasVisualEffect == true" ,EditConditionHides))
	FTransform VisualEffectTransform = FTransform::Identity;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, meta=(EditCondition = "bHasVisualEffect == true" ,EditConditionHides))
	bool bAutoDestroyVisualEffect = false;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, meta=(EditCondition = "bHasVisualEffect == true" ,EditConditionHides))
	bool bAutoActivateVisualEffect = true;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, meta=(EditCondition = "bHasVisualEffect == true" ,EditConditionHides))
	bool bPreCullCheckVisualEffect = true;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, meta=(EditCondition = "bHasVisualEffect == true" ,EditConditionHides))
	ENCPoolMethod VisualEffectPoolingMethod = ENCPoolMethod::AutoRelease;	
};

USTRUCT(BlueprintType)
struct FCD_GridTileEntry : public FFastArraySerializerItem
{
	GENERATED_BODY()

	UPROPERTY(BlueprintReadOnly, Transient)
	int32 TileIndex = INDEX_NONE;
	
	UPROPERTY(BlueprintReadOnly)
	FIntPoint GridCoordinates = FIntPoint::ZeroValue;

	UPROPERTY(BlueprintReadOnly, Transient)
	TWeakObjectPtr<UInstancedStaticMeshComponent> OwningISMC = nullptr;
	
	UPROPERTY(BlueprintReadOnly, Transient)
	TWeakObjectPtr<AActor> TileStateInstigator = nullptr;

	UPROPERTY(BlueprintReadOnly, Transient)
	FGameplayTag OriginalTeamTag = FGameplayTag();
	
	UPROPERTY(Transient)
	TArray<TWeakObjectPtr<AActor>> OccupyingActors = TArray<TWeakObjectPtr<AActor>>();

	UPROPERTY(BlueprintReadOnly, Transient)
	FVector WorldLocation = FVector::ZeroVector;
	
	UPROPERTY(BlueprintReadOnly, Transient)
	FGameplayTagContainer TileTags = FGameplayTagContainer();
	
	bool IsValid() const
	{
		return GridCoordinates != FIntPoint(INDEX_NONE, INDEX_NONE);		
	}
	
	bool HasTag(const FGameplayTag& InTagToCheck) const
	{
		return TileTags.HasTagExact(InTagToCheck);
	}
	
	bool HasAllTags(const FGameplayTagContainer& InTagsToCheck) const
	{
		return TileTags.HasAllExact(InTagsToCheck);
	}

	bool HasAnyTags(const FGameplayTagContainer& InTagsToCheck) const
	{
		return TileTags.HasAnyExact(InTagsToCheck);
	}
	
	bool IsTileOccupied() const
	{
		return !OccupyingActors.IsEmpty();
	}
};

// Client-side delegates, invoked by FCD_GridTileEntry's FFS callbacks via the OwnerGrid
DECLARE_MULTICAST_DELEGATE_OneParam(FGridTileEventSignature, const FCD_GridTileEntry& /*TileEntry*/);

USTRUCT()
struct FCD_GridTileArray : public FFastArraySerializer
{
	GENERATED_BODY()

	FCD_GridTileArray() : OwningGrid(nullptr) {}
	explicit FCD_GridTileArray(ACD_Grid* InOwnerActor) : OwningGrid(InOwnerActor) {}

	void UpdateTilesState(const FGameplayTag InTileStateTag, const TArray<FIntPoint>& InGridCoordinates, AActor* InInstigator = nullptr);
	void UpdateOccupyingActorsAtTile(const FIntPoint& InGridCoordinates, const TWeakObjectPtr<AActor> InNewActor);
	void AddTileEntry(const FCD_GridTileEntry& InEntry);
	void AddTagToTile(const FIntPoint& InGridCoordinates, const FGameplayTag& InTagToAdd);
	void Server_RemoveTileEntry(const FIntPoint& InGridCoordinates);
	void Server_RemoveTagFromTile(const FIntPoint& InGridCoordinates, const FGameplayTag& InTagToRemove);
	
	int32 GetTileIndex(const FIntPoint& InGridCoordinates);
	bool IsTileOccupied(const FIntPoint& InGridCoordinates);
	bool IsTilePassable(const FIntPoint& InGridCoordinates, const FGameplayTagContainer& InPassableBlockerTags);
	FCD_GridTileEntry* GetTileByIndex(const int32 InIndex);
	FCD_GridTileEntry* GetTileByGridCoordinates(const FIntPoint& InGridCoordinates);
	TArray<FCD_GridTileEntry> GetTilesByGridCoordinates(const TArray<FIntPoint>& InGridCoordinates);
	TArray<FCD_GridTileEntry> GetTilesByTag(const FGameplayTag& InTag);
	TArray<FCD_GridTileEntry> GetTilesByTagContainer(const FGameplayTagContainer& InTagContainer);
	const TArray<FCD_GridTileEntry>& GetAllTiles() const { return Tiles; }

	// FFastArraySerializer Contract
	void PreReplicatedRemove(const TArrayView<int32>& RemovedIndices, int32 FinalSize);
	void PostReplicatedAdd(const TArrayView<int32>& AddedIndices, int32 FinalSize);
	void PostReplicatedChange(const TArrayView<int32>& ChangedIndices, int32 FinalSize);
	// End of FFastArraySerializerContract
	
	bool NetDeltaSerialize(FNetDeltaSerializeInfo& DeltaParms)
	{
		return FastArrayDeltaSerialize<FCD_GridTileEntry, FCD_GridTileArray>(Tiles, DeltaParms, *this);
	}

	FGridTileEventSignature OnTileAddedDelegate;
	FGridTileEventSignature OnTileChangedDelegate;
	FGridTileEventSignature OnTileRemovedDelegate;
	
private:

	friend class ACD_Grid;
	friend class UCD_GridMovementComponent;
	
	UPROPERTY()
	TArray<FCD_GridTileEntry> Tiles;

	UPROPERTY(NotReplicated)
	TWeakObjectPtr<ACD_Grid> OwningGrid;
};

template<>
struct TStructOpsTypeTraits<FCD_GridTileArray> : TStructOpsTypeTraitsBase2<FCD_GridTileArray>
{
	enum
	{
		WithNetDeltaSerializer = true
	};
};
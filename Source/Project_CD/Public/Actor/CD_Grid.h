// Copyright Horizon Seeker Studios

#pragma once

#include "CoreMinimal.h"
#include "CD_BaseActor.h"
#include "GameplayTagContainer.h"
#include "Types/CD_Grid_Types.h"
#include "CD_Grid.generated.h"

class UDataAsset_GridRules;

UCLASS()
class PROJECT_CD_API ACD_Grid : public ACD_BaseActor
{
	GENERATED_BODY()

public:

	ACD_Grid();
	
	FCD_GridTileEntry* GetTileAtCoordinates(const FIntPoint& InGridCoordinates);
	TArray<FCD_GridTileEntry> GetTilesByState(const FGameplayTag& InStateTag);
	bool IsTileOccupied(const FIntPoint& InGridPosition);
	void UpdateOccupyingActorsAtTile(const FIntPoint& InGridCoordinates, const TWeakObjectPtr<AActor>& InNewActor);

	UFUNCTION(BlueprintCallable, Category = "Grid|Gameplay")
	void ApplyStateToTiles(const FGameplayTag& InStateToApply, const TArray<FIntPoint>& InTargetCoordinates, AActor* InInstigator = nullptr);

	UFUNCTION(BlueprintCallable, Category = "Grid|Configuration")
	void SetGridConfiguration(const FGameplayTag& InGridConfigurationTag);

	/*UFUNCTION(BlueprintCallable, Category = "Grid|Configuration")
	void SpawnActorOnTile(const FIntPoint& InGridCoordinates, const TSoftClassPtr<AActor>& InActorClass, const FTransform& InActorTransform);*/
	
	UPROPERTY(Replicated)
	FCD_GridTileArray GridTilesArray;
	
protected:
	
	virtual void BeginPlay() override;
	virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
	virtual void OnConstruction(const FTransform& Transform) override;
	virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;
	
private:

	void CacheGridRules();

#if WITH_EDITOR
	
	void Editor_SpawnGridVisuals();
	void Editor_UpdateTileVisuals(const int32 InTileIndex, const FVector& InTileLocation, UInstancedStaticMeshComponent* InISMC, const FGameplayTag& InStateTag);
	void Editor_SpawnActorsOnTile(const FVector& InTileLocation, TArray<FCD_ActorToSpawn>& InActorsToSpawn);

#endif
	
	void Client_HandleTileAdded(const FCD_GridTileEntry& InAddedTile);
	void Client_HandleTileChanged(const FCD_GridTileEntry& InChangedTile);
	void Client_HandleTileRemoved(const FCD_GridTileEntry& InRemovedTile);
	void Client_UpdateTileVisuals(const FCD_GridTileEntry* InTileData);
	void Client_SpawnNiagaraEffectForState(const FCD_GridTileEntry& InTileEntry, const FCD_TileState& InTileState);
	void Client_ClearNiagaraEffectForState(const FIntPoint& InCoordinates, const FGameplayTag& InStateTag);
	void Client_OnNiagaraSystemLoaded(const FIntPoint& InCoordinates, const FCD_TileState& InTileState);

	void Server_InitializeGridData();
	void Server_CheckActiveTileStates();
	void Server_AddActiveTimedState(const FGameplayTag& InStateTag, const TWeakObjectPtr<AActor>& InInstigator, const FIntPoint& InCoordinates, const int32 TileIndex, float InDuration);
	void Server_ClearSpecificTimedState(const FIntPoint& InCoordinates, const FGameplayTag& InStateTagToRemove);
	void Server_ApplyGameplayEffectToActorOnTile(TArray<TWeakObjectPtr<AActor>> InOccupyingActors, const TSoftClassPtr<UGameplayEffect>& InGameplayEffectClass, float InGameplayEffectLevel) const;
	void Server_RemoveGameplayEffectFromActorOnTile(TArray<TWeakObjectPtr<AActor>> InOccupyingActors, const TSoftClassPtr<UGameplayEffect>& InGameplayEffectSoftClass) const;
	void Server_GrantAbilityToActorOnTile(TArray<TWeakObjectPtr<AActor>> InOccupyingActors, TSoftClassPtr<UGameplayAbility> GameplayAbilitySoftClass, int32 AbilityLevel);

	/*UFUNCTION(Server, Reliable)
	void Server_SpawnActorOnTile(const FIntPoint& InGridCoordinates, const TSoftClassPtr<AActor>& InActorClass, const FTransform& InActorTransform);*/
	
	UFUNCTION(Server, Reliable)
	void Server_SetGridConfiguration(const FGameplayTag& InGridConfigurationTag);
	
	UFUNCTION(Server, Reliable)
	void Server_ApplyStateToTiles(const FGameplayTag& InStateToApply, const TArray<FIntPoint>& InTargetCoordinates, AActor* InInstigator = nullptr);
	
	FTimerHandle Server_ActiveStatesCheckTimerHandle;
	
	TMap<FIntPoint, TMap<FGameplayTag, TWeakObjectPtr<UNiagaraComponent>>> ActiveTileNiagaraComponents;

	UPROPERTY(BlueprintReadOnly, meta=(AllowPrivateAccess = true))
	TMap<FIntPoint, FCD_TileActiveStates> Server_TilesWithActiveStateEffects;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "Grid|Configuration", meta=(AllowPrivateAccess = true, Categories = "Grid.Configuration"))
	FGameplayTag CurrentGridConfigurationTag = FGameplayTag();
	
	UPROPERTY(BlueprintReadOnly, Transient, meta=(AllowPrivateAccess = true))
	TMap<FGameplayTag, FCD_TileState> TileStatesMap;

	UPROPERTY(BlueprintReadOnly, Transient, meta=(AllowPrivateAccess = true))
	TMap<FGameplayTag, FCD_GridConfiguration> GridConfigurationsMap;

	UPROPERTY(BlueprintReadOnly, Transient, Category= "Grid|Rules", meta=(AllowPrivateAccess = true))
	TObjectPtr<UDataAsset_GridRules> GridRules;
	
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Grid|Visuals", meta=(AllowPrivateAccess = true))
	TObjectPtr<UMaterialInterface> TileMaterial_TeamBlue;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Grid|Visuals", meta=(AllowPrivateAccess = true))
	TObjectPtr<UMaterialInterface> TileMaterial_TeamRed;
	
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, meta=(AllowPrivateAccess = true))
	TObjectPtr<UInstancedStaticMeshComponent> ISMC_TeamBlue;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, meta=(AllowPrivateAccess = true))
	TObjectPtr<UInstancedStaticMeshComponent> ISMC_TeamRed;
	

#if WITH_EDITORONLY_DATA
	
	UPROPERTY(EditAnywhere, Category = "Grid|Configuration|Editor")
	TObjectPtr<UDataAsset_GridRules> Editor_GridRules;

	UPROPERTY(Transient)
	TArray<TWeakObjectPtr<UNiagaraComponent>> Editor_TileActiveNiagaraComponents;

	UPROPERTY(Transient)
	TArray<TWeakObjectPtr<AActor>> Editor_TileOccupyingActors;
	
#endif
};
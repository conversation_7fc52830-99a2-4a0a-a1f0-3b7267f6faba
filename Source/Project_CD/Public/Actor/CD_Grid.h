// Copyright Horizon Seeker Studios

#pragma once

#include "CoreMinimal.h"
#include "CD_BaseActor.h"
#include "GameplayTagContainer.h"
#include "Types/CD_Grid_Types.h"
#include "CD_Grid.generated.h"

UCLASS()
class PROJECT_CD_API ACD_Grid : public ACD_BaseActor
{
	GENERATED_BODY()

public:

	ACD_Grid();

	FCD_GridTileEntry* GetTileAtCoordinates(const FIntPoint& InGridCoordinates);
	TArray<FCD_GridTileEntry> GetTilesByState(const FGameplayTag& InStateTag);
	bool IsTileOccupied(const FIntPoint& InGridPosition);

	virtual void GetLifetimeReplicatedProps(TArray<class FLifetimeProperty>& OutLifetimeProps) const override;
	
	UFUNCTION(BlueprintCallable)
	void UpdateTilesState(const FGameplayTag& InStateTag, const TArray<FIntPoint>& InGridCoordinates);

	FTimerHandle TileStateResetHandle;
	
	UPROPERTY(Replicated)
	FCD_GridTileArray GridTilesArray;
	
protected:
	
	virtual void BeginPlay() override;
	virtual void OnConstruction(const FTransform& Transform) override;

private:
	
	void SpawnGridVisuals();
	void ResetTileStateToDefault();
	void SpawnTileStateVisualEffect(const TSoftObjectPtr<UNiagaraSystem>& InNiagaraSystem, const FCD_GridTileEntry& InTileEntry, const FCD_TileState& InTileState);
	void UpdateTileInstanceVisuals(TArray<FCD_GridTileEntry>& InDirtyTiles);

	UFUNCTION()
	void Client_HandleTileDataChanged(TArray<FCD_GridTileEntry>& InDirtyTiles);
	
	UFUNCTION(Server, Reliable)
	void Server_InitializeGridData();
	
	UFUNCTION(Server, Reliable)
	void Server_UpdateTileState(const FGameplayTag& InStateTag, const TArray<FIntPoint>& InGridCoordinates);

	UPROPERTY(BlueprintReadOnly, meta=(AllowPrivateAccess = true))
	TArray<FCD_GridTileEntry> TilesToInitialize;
	
	UPROPERTY(BlueprintReadOnly, meta=(AllowPrivateAccess = true))
	TArray<FCD_ActiveTileStateInfo> TilesWithActiveEffects;
	
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Grid|Functionality|Configuration", meta=(AllowPrivateAccess = true))
	FVector GridTileSize = FVector(1.f, 1.f, 10.f);

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Grid|Functionality|Configuration", meta=(AllowPrivateAccess = true))
	FIntPoint GridSize = {6, 3};

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Grid|Functionality|Configuration", meta=(AllowPrivateAccess = true))
	FVector2D TileSpacing = FVector2D(100.f, 100.f);
	
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Grid|Functionality|Affiliation", meta=(AllowPrivateAccess = true))
	FCD_GridTileTeamAffiliation NeutralTeam;
	
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Grid|Functionality|Affiliation", meta=(AllowPrivateAccess = true))
	FCD_GridTileTeamAffiliation BlueTeam;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Grid|Functionality|Affiliation", meta=(AllowPrivateAccess = true))
	FCD_GridTileTeamAffiliation RedTeam;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Grid|Functionality|States", meta=(AllowPrivateAccess = true, Categories = "Tile.State"))
	float ResetTileStateTimerRate = 1.f;
	
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Grid|Functionality|States", meta=(AllowPrivateAccess = true, Categories = "Tile.State"))
	TMap<FGameplayTag, FCD_TileState> TileStatesList;
	
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Grid|Visuals", meta=(AllowPrivateAccess = true))
	FGameplayTag DefaultTileStateTag;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Grid|Visuals", meta=(AllowPrivateAccess = true))
	TObjectPtr<UMaterialInterface> TileMaterial;
	
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, meta=(AllowPrivateAccess = true))
	TObjectPtr<UInstancedStaticMeshComponent> InstancedStaticMeshComponent;
};
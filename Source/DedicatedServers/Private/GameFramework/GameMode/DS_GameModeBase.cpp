// Copyright Ark Wing Studios


#include "GameFramework/GameMode/DS_GameModeBase.h"

#include "aws/gamelift/server/GameLiftServerAPI.h"
#include "GameFramework/PlayerController/DS_PlayerController.h"
#include "Kismet/GameplayStatics.h"

void ADS_GameModeBase::RemovePlayerSession(AController* Exiting)
{
	ADS_PlayerController* DSPlayerController = Cast<ADS_PlayerController>(Exiting);
	if (!IsValid(DSPlayerController)) return;

#if WITH_GAMELIFT

	const FString& PlayerSessionId = DSPlayerController->PlayerSessionId;
	if (!PlayerSessionId.IsEmpty())
	{
		Aws::GameLift::Server::RemovePlayerSession(TCHAR_TO_ANSI(*PlayerSessionId));
	}
	
#endif
}

UDataAsset_CharacterClassInfo* ADS_GameModeBase::GetCharacterClassInfo() const
{
	return CharacterClassInfo;
}

UDataAsset_ProjectileInfo* ADS_GameModeBase::GetProjectileInfo() const
{
	return ProjectileInfo;
}

void ADS_GameModeBase::FindDestinationMapAndAttemptTravel(const FGameplayTag InGameplayTag) const
{
	if (const TSoftObjectPtr<UWorld> DestinationMap = DestinationMaps.FindRef(InGameplayTag);
		IsValid(DestinationMap.Get()))
	{
		AttemptSeamlessTravel(DestinationMap);
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("%hs failed, DestinationMap not found for %s"),__FUNCTION__,
			*InGameplayTag.ToString());
	}
}

void ADS_GameModeBase::AttemptSeamlessTravel(const TSoftObjectPtr<UWorld>& InDestinationMap) const
{
	const FString MapName = InDestinationMap.ToSoftObjectPath().GetAssetName();
		
	if (GIsEditor)
	{
		UGameplayStatics::OpenLevelBySoftObjectPtr(this, InDestinationMap);
	}
	else
	{
		GetWorld()->ServerTravel(MapName);
	}
}

void ADS_GameModeBase::StartCountdownTimer(FDS_CountdownTimerHandle& InCountdownTimerHandle)
{
	InCountdownTimerHandle.TimerFinishedDelegate.BindWeakLambda(this, [&]()
	{
		OnCountdownTimerFinished(InCountdownTimerHandle.Type);
	});

	GetWorldTimerManager().SetTimer(InCountdownTimerHandle.TimerFinishedHandle,
		InCountdownTimerHandle.TimerFinishedDelegate, InCountdownTimerHandle.CountdownTime, false);

	InCountdownTimerHandle.TimerUpdateDelegate.BindWeakLambda(this, [&]()
	{
		UpdateCountdownTimer(InCountdownTimerHandle);
	});

	GetWorldTimerManager().SetTimer(InCountdownTimerHandle.TimerUpdateHandle,
		InCountdownTimerHandle.TimerUpdateDelegate, InCountdownTimerHandle.CountdownUpdateInterval, true);
	
	UpdateCountdownTimer(InCountdownTimerHandle);
}

void ADS_GameModeBase::StopCountdownTimer(FDS_CountdownTimerHandle& InCountdownTimerHandle)
{
	InCountdownTimerHandle.State = EDS_CountdownTimerState::Stopped;
	GetWorldTimerManager().ClearTimer(InCountdownTimerHandle.TimerFinishedHandle);
	GetWorldTimerManager().ClearTimer(InCountdownTimerHandle.TimerUpdateHandle);
	if (InCountdownTimerHandle.TimerFinishedDelegate.IsBound())
	{
		InCountdownTimerHandle.TimerFinishedDelegate.Unbind();
	}
	if (InCountdownTimerHandle.TimerUpdateDelegate.IsBound())
	{
		InCountdownTimerHandle.TimerUpdateDelegate.Unbind();
	}

	for (FConstPlayerControllerIterator Iterator = GetWorld()->GetPlayerControllerIterator(); Iterator; ++Iterator)
	{
		ADS_PlayerController* DSPlayerController = Cast<ADS_PlayerController>(Iterator->Get());
		if (IsValid(DSPlayerController))
		{
			DSPlayerController->Client_TimerStopped(0.f, InCountdownTimerHandle.Type);
		}
	}
}

void ADS_GameModeBase::UpdateCountdownTimer(const FDS_CountdownTimerHandle& InCountdownTimerHandle) const
{
	for (FConstPlayerControllerIterator Iterator = GetWorld()->GetPlayerControllerIterator(); Iterator; ++Iterator)
	{
		ADS_PlayerController* DSPlayerController = Cast<ADS_PlayerController>(Iterator->Get());
		if (IsValid(DSPlayerController))
		{
			const float CountdownTimeLeft = InCountdownTimerHandle.CountdownTime - GetWorldTimerManager().GetTimerElapsed(InCountdownTimerHandle.TimerFinishedHandle);
			DSPlayerController->Client_TimerUpdated(CountdownTimeLeft, InCountdownTimerHandle.Type);
		}
	}
}

void ADS_GameModeBase::OnCountdownTimerFinished(EDS_CountdownTimerType& InType)
{
	
}
